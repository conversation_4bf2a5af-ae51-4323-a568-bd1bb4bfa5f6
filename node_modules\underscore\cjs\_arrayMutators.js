Object.defineProperty(exports, '__esModule', { value: true });

var pop = require('./pop.js');
var push = require('./push.js');
var reverse = require('./reverse.js');
var shift = require('./shift.js');
var sort = require('./sort.js');
var splice = require('./splice.js');
var unshift = require('./unshift.js');



exports.pop = pop;
exports.push = push;
exports.reverse = reverse;
exports.shift = shift;
exports.sort = sort;
exports.splice = splice;
exports.unshift = unshift;
