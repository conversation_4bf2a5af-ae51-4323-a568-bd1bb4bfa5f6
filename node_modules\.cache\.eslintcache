[{"D:\\ASL\\a11y-SL\\src\\index.js": "1", "D:\\ASL\\a11y-SL\\src\\App.js": "2", "D:\\ASL\\a11y-SL\\src\\reportWebVitals.js": "3", "D:\\ASL\\a11y-SL\\src\\components\\ContactPage.js": "4", "D:\\ASL\\a11y-SL\\src\\components\\TrainingPage.js": "5", "D:\\ASL\\a11y-SL\\src\\components\\AboutPage.js": "6", "D:\\ASL\\a11y-SL\\src\\components\\HomePage.js": "7", "D:\\ASL\\a11y-SL\\src\\components\\FlashCardTraining.js": "8", "D:\\ASL\\a11y-SL\\src\\config.js": "9", "D:\\ASL\\a11y-SL\\src\\components\\LevelSelector.js": "10", "D:\\ASL\\a11y-SL\\src\\hooks\\useSignDetection.js": "11", "D:\\ASL\\a11y-SL\\src\\styles\\theme.js": "12", "D:\\ASL\\a11y-SL\\src\\components\\FlashCard.js": "13", "D:\\ASL\\a11y-SL\\src\\data\\signLevels.js": "14", "D:\\ASL\\a11y-SL\\src\\components\\ui\\ModernComponents.js": "15"}, {"size": 535, "mtime": 1751014435070, "results": "16", "hashOfConfig": "17"}, {"size": 2026, "mtime": 1753995467933, "results": "18", "hashOfConfig": "17"}, {"size": 362, "mtime": 1751014435142, "results": "19", "hashOfConfig": "17"}, {"size": 10987, "mtime": 1753626749394, "results": "20", "hashOfConfig": "17"}, {"size": 73517, "mtime": 1753996537586, "results": "21", "hashOfConfig": "17"}, {"size": 6972, "mtime": 1753626749394, "results": "22", "hashOfConfig": "17"}, {"size": 16627, "mtime": 1752245255061, "results": "23", "hashOfConfig": "17"}, {"size": 30230, "mtime": 1756027331915, "results": "24", "hashOfConfig": "17"}, {"size": 983, "mtime": 1753996540425, "results": "25", "hashOfConfig": "17"}, {"size": 13852, "mtime": 1753626815580, "results": "26", "hashOfConfig": "17"}, {"size": 7826, "mtime": 1753996532615, "results": "27", "hashOfConfig": "17"}, {"size": 5812, "mtime": 1753627046339, "results": "28", "hashOfConfig": "17"}, {"size": 10435, "mtime": 1756027208465, "results": "29", "hashOfConfig": "17"}, {"size": 40371, "mtime": 1753599920690, "results": "30", "hashOfConfig": "17"}, {"size": 11379, "mtime": 1753626748737, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1achv07", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\ASL\\a11y-SL\\src\\index.js", [], [], "D:\\ASL\\a11y-SL\\src\\App.js", [], [], "D:\\ASL\\a11y-SL\\src\\reportWebVitals.js", [], [], "D:\\ASL\\a11y-SL\\src\\components\\ContactPage.js", [], [], "D:\\ASL\\a11y-SL\\src\\components\\TrainingPage.js", ["77"], ["78"], "D:\\ASL\\a11y-SL\\src\\components\\AboutPage.js", ["79", "80", "81", "82"], [], "D:\\ASL\\a11y-SL\\src\\components\\HomePage.js", ["83", "84", "85", "86"], [], "D:\\ASL\\a11y-SL\\src\\components\\FlashCardTraining.js", ["87", "88", "89", "90", "91"], ["92"], "D:\\ASL\\a11y-SL\\src\\config.js", [], [], "D:\\ASL\\a11y-SL\\src\\components\\LevelSelector.js", ["93", "94", "95", "96"], [], "D:\\ASL\\a11y-SL\\src\\hooks\\useSignDetection.js", ["97"], [], "D:\\ASL\\a11y-SL\\src\\styles\\theme.js", [], [], "D:\\ASL\\a11y-SL\\src\\components\\FlashCard.js", ["98", "99", "100", "101"], [], "D:\\ASL\\a11y-SL\\src\\data\\signLevels.js", [], [], "D:\\ASL\\a11y-SL\\src\\components\\ui\\ModernComponents.js", ["102"], [], {"ruleId": "103", "severity": 1, "message": "104", "line": 21, "column": 8, "nodeType": "105", "messageId": "106", "endLine": 21, "endColumn": 14}, {"ruleId": "103", "severity": 1, "message": "107", "line": 1044, "column": 26, "nodeType": "105", "messageId": "106", "endLine": 1044, "endColumn": 43, "suppressions": "108"}, {"ruleId": "103", "severity": 1, "message": "109", "line": 10, "column": 3, "nodeType": "105", "messageId": "106", "endLine": 10, "endColumn": 6}, {"ruleId": "103", "severity": 1, "message": "110", "line": 11, "column": 3, "nodeType": "105", "messageId": "106", "endLine": 11, "endColumn": 8}, {"ruleId": "103", "severity": 1, "message": "111", "line": 12, "column": 3, "nodeType": "105", "messageId": "106", "endLine": 12, "endColumn": 6}, {"ruleId": "103", "severity": 1, "message": "112", "line": 229, "column": 7, "nodeType": "105", "messageId": "106", "endLine": 229, "endColumn": 19}, {"ruleId": "103", "severity": 1, "message": "113", "line": 6, "column": 3, "nodeType": "105", "messageId": "106", "endLine": 6, "endColumn": 8}, {"ruleId": "103", "severity": 1, "message": "109", "line": 14, "column": 3, "nodeType": "105", "messageId": "106", "endLine": 14, "endColumn": 6}, {"ruleId": "103", "severity": 1, "message": "114", "line": 15, "column": 3, "nodeType": "105", "messageId": "106", "endLine": 15, "endColumn": 8}, {"ruleId": "103", "severity": 1, "message": "115", "line": 16, "column": 3, "nodeType": "105", "messageId": "106", "endLine": 16, "endColumn": 13}, {"ruleId": "103", "severity": 1, "message": "111", "line": 16, "column": 3, "nodeType": "105", "messageId": "106", "endLine": 16, "endColumn": 6}, {"ruleId": "103", "severity": 1, "message": "116", "line": 17, "column": 3, "nodeType": "105", "messageId": "106", "endLine": 17, "endColumn": 11}, {"ruleId": "103", "severity": 1, "message": "114", "line": 18, "column": 3, "nodeType": "105", "messageId": "106", "endLine": 18, "endColumn": 8}, {"ruleId": "103", "severity": 1, "message": "117", "line": 75, "column": 7, "nodeType": "105", "messageId": "106", "endLine": 75, "endColumn": 13}, {"ruleId": "103", "severity": 1, "message": "118", "line": 662, "column": 5, "nodeType": "105", "messageId": "106", "endLine": 662, "endColumn": 15}, {"ruleId": "119", "severity": 1, "message": "120", "line": 837, "column": 6, "nodeType": "121", "endLine": 837, "endColumn": 216, "suggestions": "122", "suppressions": "123"}, {"ruleId": "103", "severity": 1, "message": "116", "line": 3, "column": 57, "nodeType": "105", "messageId": "106", "endLine": 3, "endColumn": 65}, {"ruleId": "103", "severity": 1, "message": "111", "line": 3, "column": 67, "nodeType": "105", "messageId": "106", "endLine": 3, "endColumn": 70}, {"ruleId": "103", "severity": 1, "message": "124", "line": 351, "column": 7, "nodeType": "105", "messageId": "106", "endLine": 351, "endColumn": 14}, {"ruleId": "103", "severity": 1, "message": "125", "line": 362, "column": 9, "nodeType": "105", "messageId": "106", "endLine": 362, "endColumn": 20}, {"ruleId": "119", "severity": 1, "message": "126", "line": 120, "column": 6, "nodeType": "121", "endLine": 120, "endColumn": 8, "suggestions": "127"}, {"ruleId": "103", "severity": 1, "message": "128", "line": 3, "column": 34, "nodeType": "105", "messageId": "106", "endLine": 3, "endColumn": 44}, {"ruleId": "103", "severity": 1, "message": "129", "line": 3, "column": 46, "nodeType": "105", "messageId": "106", "endLine": 3, "endColumn": 55}, {"ruleId": "103", "severity": 1, "message": "130", "line": 3, "column": 57, "nodeType": "105", "messageId": "106", "endLine": 3, "endColumn": 63}, {"ruleId": "103", "severity": 1, "message": "124", "line": 342, "column": 7, "nodeType": "105", "messageId": "106", "endLine": 342, "endColumn": 14}, {"ruleId": "131", "severity": 1, "message": "132", "line": 443, "column": 1, "nodeType": "133", "endLine": 457, "endColumn": 3}, "no-unused-vars", "'config' is defined but never used.", "Identifier", "unusedVar", "'setRecordedVideos' is assigned a value but never used.", ["134"], "'Eye' is defined but never used.", "'Globe' is defined but never used.", "'Zap' is defined but never used.", "'SectionTitle' is assigned a value but never used.", "'Users' is defined but never used.", "'Award' is defined but never used.", "'TrendingUp' is defined but never used.", "'Sparkles' is defined but never used.", "'Header' is assigned a value but never used.", "'targetSign' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'nextCard'. Either include it or remove the dependency array.", "ArrayExpression", ["135"], ["136"], "'shimmer' is assigned a value but never used.", "'totalLevels' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'lastPrediction'. Either include it or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setPrediction' needs the current value of 'lastPrediction'.", ["137"], "'ArrowRight' is defined but never used.", "'ArrowLeft' is defined but never used.", "'Target' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", {"kind": "138", "justification": "139"}, {"desc": "140", "fix": "141"}, {"kind": "138", "justification": "139"}, {"desc": "142", "fix": "143"}, "directive", "", "Update the dependencies array to be: [signMatched, currentSign, prediction, currentCardIndex, signs.length, level, onProgressUpdate, isAIRecording, isConnected, startAIRecording, stopAIRecording, completedCards, saveTrainingData, currentKeypoints, nextCard]", {"range": "144", "text": "145"}, "Update the dependencies array to be: [lastPrediction]", {"range": "146", "text": "147"}, [21018, 21228], "[signMatched, currentSign, prediction, currentCardIndex, signs.length, level, onProgressUpdate, isAIRecording, isConnected, startAIRecording, stopAIRecording, completedCards, saveTrainingData, currentKeypoints, nextCard]", [4742, 4744], "[lastPrediction]"]