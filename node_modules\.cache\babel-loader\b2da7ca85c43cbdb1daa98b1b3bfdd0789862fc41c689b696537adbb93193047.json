{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\a11y-SL\\\\src\\\\components\\\\FlashCard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled, { keyframes, css } from 'styled-components';\nimport { CheckCircle, RotateCcw, ArrowRight, ArrowLeft, Target, Zap, Sparkles } from 'lucide-react';\nimport { theme } from '../styles/theme';\nimport { Card, Badge, Text, Heading } from './ui/ModernComponents';\n\n// Modern Animations\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst flipIn = keyframes`\n  from {\n    transform: perspective(600px) rotateY(-90deg);\n    opacity: 0;\n  }\n  to {\n    transform: perspective(600px) rotateY(0deg);\n    opacity: 1;\n  }\n`;\nconst slideInRight = keyframes`\n  from {\n    transform: translateX(100%) scale(0.9);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0) scale(1);\n    opacity: 1;\n  }\n`;\nconst slideInLeft = keyframes`\n  from {\n    transform: translateX(-100%) scale(0.9);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0) scale(1);\n    opacity: 1;\n  }\n`;\nconst successPulse = keyframes`\n  0% {\n    transform: scale(1);\n    box-shadow: ${theme.shadows.lg};\n  }\n  50% {\n    transform: scale(1.02);\n    box-shadow: ${theme.shadows.glowSuccess};\n  }\n  100% {\n    transform: scale(1);\n    box-shadow: ${theme.shadows.lg};\n  }\n`;\nconst shake = keyframes`\n  0%, 100% { transform: translateX(0); }\n  25% { transform: translateX(-8px); }\n  75% { transform: translateX(8px); }\n`;\nconst sparkle = keyframes`\n  0%, 100% {\n    transform: scale(0) rotate(0deg);\n    opacity: 0;\n  }\n  50% {\n    transform: scale(1) rotate(180deg);\n    opacity: 1;\n  }\n`;\n\n// Modern Styled Components\nconst CardContainer = styled.div`\n  position: relative;\n  width: 100%;\n  max-width: 480px;\n  margin: 0 auto;\n  perspective: 1200px;\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    max-width: 420px;\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    max-width: 380px;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    max-width: 100%;\n    padding: 0 ${theme.spacing[2]};\n    margin: 0;\n  }\n`;\n_c = CardContainer;\nconst ModernCard = styled(Card)`\n  background: ${theme.colors.background};\n  border-radius: ${theme.borderRadius['2xl']};\n  box-shadow: ${theme.shadows.xl};\n  padding: ${theme.spacing[6]};\n  text-align: center;\n  position: relative;\n  overflow: hidden;\n  transition: all ${theme.transitions.normal};\n  border: 1px solid ${theme.colors.neutral[100]};\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: ${theme.spacing[3]};\n    border-radius: ${theme.borderRadius['lg']};\n  }\n\n  animation: ${props => {\n  if (props.isCorrect) return css`${successPulse} 0.8s ease`;\n  if (props.isIncorrect) return css`${shake} 0.6s ease`;\n  if (props.slideDirection === 'right') return css`${slideInRight} 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)`;\n  if (props.slideDirection === 'left') return css`${slideInLeft} 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)`;\n  return css`${flipIn} 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)`;\n}};\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 6px;\n    background: ${props => {\n  if (props.isCorrect) return theme.colors.gradients.success;\n  if (props.isIncorrect) return theme.colors.gradients.error;\n  return theme.colors.gradients.primary;\n}};\n    border-radius: ${theme.borderRadius['3xl']} ${theme.borderRadius['3xl']} 0 0;\n  }\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: -50%;\n    left: -50%;\n    width: 200%;\n    height: 200%;\n    background: ${props => {\n  if (props.isCorrect) return `radial-gradient(circle, ${theme.colors.success[100]} 0%, transparent 70%)`;\n  if (props.isIncorrect) return `radial-gradient(circle, ${theme.colors.error[100]} 0%, transparent 70%)`;\n  return 'transparent';\n}};\n    opacity: ${props => props.isCorrect || props.isIncorrect ? 0.3 : 0};\n    transition: opacity ${theme.transitions.normal};\n    pointer-events: none;\n    z-index: 0;\n  }\n\n  > * {\n    position: relative;\n    z-index: 1;\n  }\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    padding: ${theme.spacing[6]};\n    border-radius: ${theme.borderRadius['2xl']};\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    padding: ${theme.spacing[5]};\n    border-radius: ${theme.borderRadius['xl']};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: ${theme.spacing[5]};\n    border-radius: ${theme.borderRadius.xl};\n    box-shadow: ${theme.shadows.lg};\n  }\n`;\n_c2 = ModernCard;\nconst SignGif = styled.div`\n  width: 240px;\n  height: 240px;\n  margin: 0 auto ${theme.spacing[6]};\n  border-radius: ${theme.borderRadius['2xl']};\n  overflow: hidden;\n  background: ${theme.colors.gradients.surface};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  box-shadow: ${theme.shadows.md};\n  border: 2px solid ${theme.colors.neutral[100]};\n  transition: all ${theme.transitions.normal};\n\n  &:hover {\n    transform: scale(1.02);\n    box-shadow: ${theme.shadows.lg};\n  }\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: ${theme.borderRadius.xl};\n    transition: transform ${theme.transitions.normal};\n  }\n\n  .fallback {\n    font-size: 5rem;\n    color: ${theme.colors.text.tertiary};\n    filter: grayscale(0.3);\n  }\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    width: 220px;\n    height: 220px;\n    margin-bottom: ${theme.spacing[6]};\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    width: 200px;\n    height: 200px;\n    margin-bottom: ${theme.spacing[5]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    width: 120px;\n    height: 120px;\n    margin-bottom: ${theme.spacing[2]};\n    border-radius: ${theme.borderRadius.lg};\n  }\n`;\n_c3 = SignGif;\nconst SignName = styled(Heading)`\n  margin-bottom: ${theme.spacing[2]};\n  background: ${theme.colors.gradients.primary};\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n`;\n_c4 = SignName;\nconst SignDescription = styled(Text)`\n  margin-bottom: ${theme.spacing[6]};\n  max-width: 300px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    margin-bottom: ${theme.spacing[4]};\n  }\n`;\n_c5 = SignDescription;\nconst StatusIndicator = styled.div`\n  position: absolute;\n  top: ${theme.spacing[4]};\n  right: ${theme.spacing[4]};\n  width: 56px;\n  height: 56px;\n  border-radius: ${theme.borderRadius.full};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: ${props => {\n  if (props.isCorrect) return theme.colors.gradients.success;\n  if (props.isIncorrect) return theme.colors.gradients.error;\n  if (props.isDetecting) return theme.colors.gradients.warning;\n  return 'transparent';\n}};\n  color: ${theme.colors.text.inverse};\n  transition: all ${theme.transitions.normal};\n  box-shadow: ${props => {\n  if (props.isCorrect) return theme.shadows.glowSuccess;\n  if (props.isIncorrect) return theme.shadows.glowError;\n  return theme.shadows.md;\n}};\n  z-index: 10;\n\n  ${props => props.isDetecting && css`\n    animation: ${successPulse} 1.5s ease infinite;\n  `}\n\n  ${props => props.isCorrect && css`\n    &::before {\n      content: '';\n      position: absolute;\n      top: -10px;\n      right: -10px;\n      width: 20px;\n      height: 20px;\n      background: ${theme.colors.warning[400]};\n      border-radius: ${theme.borderRadius.full};\n      animation: ${sparkle} 1s ease;\n    }\n  `}\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    width: 48px;\n    height: 48px;\n    top: ${theme.spacing[3]};\n    right: ${theme.spacing[3]};\n  }\n`;\n_c6 = StatusIndicator;\nconst ProgressBar = styled.div`\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  height: 6px;\n  background: ${theme.colors.gradients.success};\n  transition: width ${theme.transitions.slow};\n  border-radius: 0 0 ${theme.borderRadius['3xl']} ${theme.borderRadius['3xl']};\n  box-shadow: 0 -2px 10px rgba(34, 197, 94, 0.3);\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n    animation: shimmer 2s infinite;\n  }\n`;\n_c7 = ProgressBar;\nconst CardNumber = styled(Badge)`\n  position: absolute;\n  top: ${theme.spacing[4]};\n  left: ${theme.spacing[4]};\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(10px);\n  color: ${theme.colors.primary[700]};\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  font-weight: ${theme.typography.fontWeight.semibold};\n  font-size: ${theme.typography.fontSize.sm};\n  padding: ${theme.spacing[2]} ${theme.spacing[3]};\n  box-shadow: ${theme.shadows.sm};\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    top: ${theme.spacing[3]};\n    left: ${theme.spacing[3]};\n    font-size: ${theme.typography.fontSize.xs};\n    padding: ${theme.spacing[1]} ${theme.spacing[2]};\n  }\n`;\n_c8 = CardNumber;\nconst shimmer = keyframes`\n  0% { transform: translateX(-100%); }\n  100% { transform: translateX(100%); }\n`;\nconst FlashCard = ({\n  sign,\n  cardNumber,\n  totalCards,\n  isCorrect,\n  isIncorrect,\n  isDetecting,\n  slideDirection,\n  progress = 0\n}) => {\n  _s();\n  const [imgError, setImgError] = useState(false);\n  const [animationKey, setAnimationKey] = useState(0);\n  useEffect(() => {\n    setAnimationKey(prev => prev + 1);\n    setImgError(false);\n  }, [sign.key]);\n  const getStatusIcon = () => {\n    if (isCorrect) return /*#__PURE__*/_jsxDEV(CheckCircle, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 27\n    }, this);\n    if (isIncorrect) return /*#__PURE__*/_jsxDEV(RotateCcw, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 29\n    }, this);\n    if (isDetecting) return /*#__PURE__*/_jsxDEV(Zap, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 29\n    }, this);\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(CardContainer, {\n    children: /*#__PURE__*/_jsxDEV(ModernCard, {\n      isCorrect: isCorrect,\n      isIncorrect: isIncorrect,\n      slideDirection: slideDirection,\n      children: [/*#__PURE__*/_jsxDEV(CardNumber, {\n        variant: \"primary\",\n        children: [cardNumber, \" / \", totalCards]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatusIndicator, {\n        isCorrect: isCorrect,\n        isIncorrect: isIncorrect,\n        isDetecting: isDetecting,\n        children: getStatusIcon()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SignGif, {\n        children: !imgError ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: sign.gif,\n          alt: sign.name,\n          onError: () => setImgError(true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fallback\",\n          children: /*#__PURE__*/_jsxDEV(Sparkles, {\n            size: 60\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SignName, {\n        level: 2,\n        gradient: \"primary\",\n        children: sign.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SignDescription, {\n        size: \"lg\",\n        muted: true,\n        children: sign.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n        style: {\n          width: `${progress}%`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 9\n      }, this)]\n    }, animationKey, true, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 373,\n    columnNumber: 5\n  }, this);\n};\n_s(FlashCard, \"wKVPW2uLgG2r/wRSYd4Do2hI8LE=\");\n_c9 = FlashCard;\nexport default FlashCard;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"CardContainer\");\n$RefreshReg$(_c2, \"ModernCard\");\n$RefreshReg$(_c3, \"SignGif\");\n$RefreshReg$(_c4, \"SignName\");\n$RefreshReg$(_c5, \"SignDescription\");\n$RefreshReg$(_c6, \"StatusIndicator\");\n$RefreshReg$(_c7, \"ProgressBar\");\n$RefreshReg$(_c8, \"CardNumber\");\n$RefreshReg$(_c9, \"FlashCard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "keyframes", "css", "CheckCircle", "RotateCcw", "ArrowRight", "ArrowLeft", "Target", "Zap", "<PERSON><PERSON><PERSON>", "theme", "Card", "Badge", "Text", "Heading", "jsxDEV", "_jsxDEV", "flipIn", "slideInRight", "slideInLeft", "successPulse", "shadows", "lg", "glowSuccess", "shake", "sparkle", "CardContainer", "div", "breakpoints", "md", "sm", "spacing", "_c", "ModernCard", "colors", "background", "borderRadius", "xl", "transitions", "normal", "neutral", "props", "isCorrect", "isIncorrect", "slideDirection", "gradients", "success", "error", "primary", "_c2", "SignGif", "surface", "text", "tertiary", "_c3", "SignName", "_c4", "SignDescription", "_c5", "StatusIndicator", "full", "isDetecting", "warning", "inverse", "glowError", "_c6", "ProgressBar", "slow", "_c7", "CardNumber", "typography", "fontWeight", "semibold", "fontSize", "xs", "_c8", "shimmer", "FlashCard", "sign", "cardNumber", "totalCards", "progress", "_s", "imgError", "setImgError", "animationKey", "setAnimationKey", "prev", "key", "getStatusIcon", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "variant", "src", "gif", "alt", "name", "onError", "className", "level", "gradient", "muted", "description", "style", "width", "_c9", "$RefreshReg$"], "sources": ["D:/ASL/a11y-SL/src/components/FlashCard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled, { keyframes, css } from 'styled-components';\nimport { CheckCircle, RotateCcw, ArrowRight, ArrowLeft, Target, Zap, Sparkles } from 'lucide-react';\nimport { theme } from '../styles/theme';\nimport { Card, Badge, Text, Heading } from './ui/ModernComponents';\n\n// Modern Animations\nconst flipIn = keyframes`\n  from {\n    transform: perspective(600px) rotateY(-90deg);\n    opacity: 0;\n  }\n  to {\n    transform: perspective(600px) rotateY(0deg);\n    opacity: 1;\n  }\n`;\n\nconst slideInRight = keyframes`\n  from {\n    transform: translateX(100%) scale(0.9);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0) scale(1);\n    opacity: 1;\n  }\n`;\n\nconst slideInLeft = keyframes`\n  from {\n    transform: translateX(-100%) scale(0.9);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0) scale(1);\n    opacity: 1;\n  }\n`;\n\nconst successPulse = keyframes`\n  0% {\n    transform: scale(1);\n    box-shadow: ${theme.shadows.lg};\n  }\n  50% {\n    transform: scale(1.02);\n    box-shadow: ${theme.shadows.glowSuccess};\n  }\n  100% {\n    transform: scale(1);\n    box-shadow: ${theme.shadows.lg};\n  }\n`;\n\nconst shake = keyframes`\n  0%, 100% { transform: translateX(0); }\n  25% { transform: translateX(-8px); }\n  75% { transform: translateX(8px); }\n`;\n\nconst sparkle = keyframes`\n  0%, 100% {\n    transform: scale(0) rotate(0deg);\n    opacity: 0;\n  }\n  50% {\n    transform: scale(1) rotate(180deg);\n    opacity: 1;\n  }\n`;\n\n// Modern Styled Components\nconst CardContainer = styled.div`\n  position: relative;\n  width: 100%;\n  max-width: 480px;\n  margin: 0 auto;\n  perspective: 1200px;\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    max-width: 420px;\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    max-width: 380px;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    max-width: 100%;\n    padding: 0 ${theme.spacing[2]};\n    margin: 0;\n  }\n`;\n\nconst ModernCard = styled(Card)`\n  background: ${theme.colors.background};\n  border-radius: ${theme.borderRadius['2xl']};\n  box-shadow: ${theme.shadows.xl};\n  padding: ${theme.spacing[6]};\n  text-align: center;\n  position: relative;\n  overflow: hidden;\n  transition: all ${theme.transitions.normal};\n  border: 1px solid ${theme.colors.neutral[100]};\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: ${theme.spacing[3]};\n    border-radius: ${theme.borderRadius['lg']};\n  }\n\n  animation: ${props => {\n    if (props.isCorrect) return css`${successPulse} 0.8s ease`;\n    if (props.isIncorrect) return css`${shake} 0.6s ease`;\n    if (props.slideDirection === 'right') return css`${slideInRight} 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)`;\n    if (props.slideDirection === 'left') return css`${slideInLeft} 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)`;\n    return css`${flipIn} 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)`;\n  }};\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 6px;\n    background: ${props => {\n      if (props.isCorrect) return theme.colors.gradients.success;\n      if (props.isIncorrect) return theme.colors.gradients.error;\n      return theme.colors.gradients.primary;\n    }};\n    border-radius: ${theme.borderRadius['3xl']} ${theme.borderRadius['3xl']} 0 0;\n  }\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: -50%;\n    left: -50%;\n    width: 200%;\n    height: 200%;\n    background: ${props => {\n      if (props.isCorrect) return `radial-gradient(circle, ${theme.colors.success[100]} 0%, transparent 70%)`;\n      if (props.isIncorrect) return `radial-gradient(circle, ${theme.colors.error[100]} 0%, transparent 70%)`;\n      return 'transparent';\n    }};\n    opacity: ${props => (props.isCorrect || props.isIncorrect) ? 0.3 : 0};\n    transition: opacity ${theme.transitions.normal};\n    pointer-events: none;\n    z-index: 0;\n  }\n\n  > * {\n    position: relative;\n    z-index: 1;\n  }\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    padding: ${theme.spacing[6]};\n    border-radius: ${theme.borderRadius['2xl']};\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    padding: ${theme.spacing[5]};\n    border-radius: ${theme.borderRadius['xl']};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: ${theme.spacing[5]};\n    border-radius: ${theme.borderRadius.xl};\n    box-shadow: ${theme.shadows.lg};\n  }\n`;\n\nconst SignGif = styled.div`\n  width: 240px;\n  height: 240px;\n  margin: 0 auto ${theme.spacing[6]};\n  border-radius: ${theme.borderRadius['2xl']};\n  overflow: hidden;\n  background: ${theme.colors.gradients.surface};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  box-shadow: ${theme.shadows.md};\n  border: 2px solid ${theme.colors.neutral[100]};\n  transition: all ${theme.transitions.normal};\n\n  &:hover {\n    transform: scale(1.02);\n    box-shadow: ${theme.shadows.lg};\n  }\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: ${theme.borderRadius.xl};\n    transition: transform ${theme.transitions.normal};\n  }\n\n  .fallback {\n    font-size: 5rem;\n    color: ${theme.colors.text.tertiary};\n    filter: grayscale(0.3);\n  }\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    width: 220px;\n    height: 220px;\n    margin-bottom: ${theme.spacing[6]};\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    width: 200px;\n    height: 200px;\n    margin-bottom: ${theme.spacing[5]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    width: 120px;\n    height: 120px;\n    margin-bottom: ${theme.spacing[2]};\n    border-radius: ${theme.borderRadius.lg};\n  }\n`;\n\nconst SignName = styled(Heading)`\n  margin-bottom: ${theme.spacing[2]};\n  background: ${theme.colors.gradients.primary};\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n`;\n\nconst SignDescription = styled(Text)`\n  margin-bottom: ${theme.spacing[6]};\n  max-width: 300px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    margin-bottom: ${theme.spacing[4]};\n  }\n`;\n\nconst StatusIndicator = styled.div`\n  position: absolute;\n  top: ${theme.spacing[4]};\n  right: ${theme.spacing[4]};\n  width: 56px;\n  height: 56px;\n  border-radius: ${theme.borderRadius.full};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: ${props => {\n    if (props.isCorrect) return theme.colors.gradients.success;\n    if (props.isIncorrect) return theme.colors.gradients.error;\n    if (props.isDetecting) return theme.colors.gradients.warning;\n    return 'transparent';\n  }};\n  color: ${theme.colors.text.inverse};\n  transition: all ${theme.transitions.normal};\n  box-shadow: ${props => {\n    if (props.isCorrect) return theme.shadows.glowSuccess;\n    if (props.isIncorrect) return theme.shadows.glowError;\n    return theme.shadows.md;\n  }};\n  z-index: 10;\n\n  ${props => props.isDetecting && css`\n    animation: ${successPulse} 1.5s ease infinite;\n  `}\n\n  ${props => props.isCorrect && css`\n    &::before {\n      content: '';\n      position: absolute;\n      top: -10px;\n      right: -10px;\n      width: 20px;\n      height: 20px;\n      background: ${theme.colors.warning[400]};\n      border-radius: ${theme.borderRadius.full};\n      animation: ${sparkle} 1s ease;\n    }\n  `}\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    width: 48px;\n    height: 48px;\n    top: ${theme.spacing[3]};\n    right: ${theme.spacing[3]};\n  }\n`;\n\nconst ProgressBar = styled.div`\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  height: 6px;\n  background: ${theme.colors.gradients.success};\n  transition: width ${theme.transitions.slow};\n  border-radius: 0 0 ${theme.borderRadius['3xl']} ${theme.borderRadius['3xl']};\n  box-shadow: 0 -2px 10px rgba(34, 197, 94, 0.3);\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n    animation: shimmer 2s infinite;\n  }\n`;\n\nconst CardNumber = styled(Badge)`\n  position: absolute;\n  top: ${theme.spacing[4]};\n  left: ${theme.spacing[4]};\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(10px);\n  color: ${theme.colors.primary[700]};\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  font-weight: ${theme.typography.fontWeight.semibold};\n  font-size: ${theme.typography.fontSize.sm};\n  padding: ${theme.spacing[2]} ${theme.spacing[3]};\n  box-shadow: ${theme.shadows.sm};\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    top: ${theme.spacing[3]};\n    left: ${theme.spacing[3]};\n    font-size: ${theme.typography.fontSize.xs};\n    padding: ${theme.spacing[1]} ${theme.spacing[2]};\n  }\n`;\n\nconst shimmer = keyframes`\n  0% { transform: translateX(-100%); }\n  100% { transform: translateX(100%); }\n`;\n\nconst FlashCard = ({ \n  sign, \n  cardNumber, \n  totalCards, \n  isCorrect, \n  isIncorrect, \n  isDetecting,\n  slideDirection,\n  progress = 0 \n}) => {\n  const [imgError, setImgError] = useState(false);\n  const [animationKey, setAnimationKey] = useState(0);\n\n  useEffect(() => {\n    setAnimationKey(prev => prev + 1);\n    setImgError(false);\n  }, [sign.key]);\n\n  const getStatusIcon = () => {\n    if (isCorrect) return <CheckCircle size={20} />;\n    if (isIncorrect) return <RotateCcw size={20} />;\n    if (isDetecting) return <Zap size={20} />;\n    return null;\n  };\n\n  return (\n    <CardContainer>\n      <ModernCard\n        key={animationKey}\n        isCorrect={isCorrect}\n        isIncorrect={isIncorrect}\n        slideDirection={slideDirection}\n      >\n        <CardNumber variant=\"primary\">\n          {cardNumber} / {totalCards}\n        </CardNumber>\n\n        <StatusIndicator\n          isCorrect={isCorrect}\n          isIncorrect={isIncorrect}\n          isDetecting={isDetecting}\n        >\n          {getStatusIcon()}\n        </StatusIndicator>\n\n        <SignGif>\n          {!imgError ? (\n            <img\n              src={sign.gif}\n              alt={sign.name}\n              onError={() => setImgError(true)}\n            />\n          ) : (\n            <div className=\"fallback\">\n              <Sparkles size={60} />\n            </div>\n          )}\n        </SignGif>\n\n        <SignName level={2} gradient=\"primary\">\n          {sign.name}\n        </SignName>\n\n        <SignDescription size=\"lg\" muted>\n          {sign.description}\n        </SignDescription>\n\n        <ProgressBar style={{ width: `${progress}%` }} />\n      </ModernCard>\n    </CardContainer>\n  );\n};\n\nexport default FlashCard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,IAAIC,SAAS,EAAEC,GAAG,QAAQ,mBAAmB;AAC1D,SAASC,WAAW,EAAEC,SAAS,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,cAAc;AACnG,SAASC,KAAK,QAAQ,iBAAiB;AACvC,SAASC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,OAAO,QAAQ,uBAAuB;;AAElE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,MAAM,GAAGhB,SAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMiB,YAAY,GAAGjB,SAAS;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMkB,WAAW,GAAGlB,SAAS;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMmB,YAAY,GAAGnB,SAAS;AAC9B;AACA;AACA,kBAAkBS,KAAK,CAACW,OAAO,CAACC,EAAE;AAClC;AACA;AACA;AACA,kBAAkBZ,KAAK,CAACW,OAAO,CAACE,WAAW;AAC3C;AACA;AACA;AACA,kBAAkBb,KAAK,CAACW,OAAO,CAACC,EAAE;AAClC;AACA,CAAC;AAED,MAAME,KAAK,GAAGvB,SAAS;AACvB;AACA;AACA;AACA,CAAC;AAED,MAAMwB,OAAO,GAAGxB,SAAS;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMyB,aAAa,GAAG1B,MAAM,CAAC2B,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBjB,KAAK,CAACkB,WAAW,CAACN,EAAE;AAC3C;AACA;AACA;AACA,uBAAuBZ,KAAK,CAACkB,WAAW,CAACC,EAAE;AAC3C;AACA;AACA;AACA,uBAAuBnB,KAAK,CAACkB,WAAW,CAACE,EAAE;AAC3C;AACA,iBAAiBpB,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AACjC;AACA;AACA,CAAC;AAACC,EAAA,GApBIN,aAAa;AAsBnB,MAAMO,UAAU,GAAGjC,MAAM,CAACW,IAAI,CAAC;AAC/B,gBAAgBD,KAAK,CAACwB,MAAM,CAACC,UAAU;AACvC,mBAAmBzB,KAAK,CAAC0B,YAAY,CAAC,KAAK,CAAC;AAC5C,gBAAgB1B,KAAK,CAACW,OAAO,CAACgB,EAAE;AAChC,aAAa3B,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AAC7B;AACA;AACA;AACA,oBAAoBrB,KAAK,CAAC4B,WAAW,CAACC,MAAM;AAC5C,sBAAsB7B,KAAK,CAACwB,MAAM,CAACM,OAAO,CAAC,GAAG,CAAC;AAC/C;AACA,uBAAuB9B,KAAK,CAACkB,WAAW,CAACE,EAAE;AAC3C,eAAepB,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AAC/B,qBAAqBrB,KAAK,CAAC0B,YAAY,CAAC,IAAI,CAAC;AAC7C;AACA;AACA,eAAeK,KAAK,IAAI;EACpB,IAAIA,KAAK,CAACC,SAAS,EAAE,OAAOxC,GAAG,GAAGkB,YAAY,YAAY;EAC1D,IAAIqB,KAAK,CAACE,WAAW,EAAE,OAAOzC,GAAG,GAAGsB,KAAK,YAAY;EACrD,IAAIiB,KAAK,CAACG,cAAc,KAAK,OAAO,EAAE,OAAO1C,GAAG,GAAGgB,YAAY,yCAAyC;EACxG,IAAIuB,KAAK,CAACG,cAAc,KAAK,MAAM,EAAE,OAAO1C,GAAG,GAAGiB,WAAW,yCAAyC;EACtG,OAAOjB,GAAG,GAAGe,MAAM,yCAAyC;AAC9D,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBwB,KAAK,IAAI;EACrB,IAAIA,KAAK,CAACC,SAAS,EAAE,OAAOhC,KAAK,CAACwB,MAAM,CAACW,SAAS,CAACC,OAAO;EAC1D,IAAIL,KAAK,CAACE,WAAW,EAAE,OAAOjC,KAAK,CAACwB,MAAM,CAACW,SAAS,CAACE,KAAK;EAC1D,OAAOrC,KAAK,CAACwB,MAAM,CAACW,SAAS,CAACG,OAAO;AACvC,CAAC;AACL,qBAAqBtC,KAAK,CAAC0B,YAAY,CAAC,KAAK,CAAC,IAAI1B,KAAK,CAAC0B,YAAY,CAAC,KAAK,CAAC;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBK,KAAK,IAAI;EACrB,IAAIA,KAAK,CAACC,SAAS,EAAE,OAAO,2BAA2BhC,KAAK,CAACwB,MAAM,CAACY,OAAO,CAAC,GAAG,CAAC,uBAAuB;EACvG,IAAIL,KAAK,CAACE,WAAW,EAAE,OAAO,2BAA2BjC,KAAK,CAACwB,MAAM,CAACa,KAAK,CAAC,GAAG,CAAC,uBAAuB;EACvG,OAAO,aAAa;AACtB,CAAC;AACL,eAAeN,KAAK,IAAKA,KAAK,CAACC,SAAS,IAAID,KAAK,CAACE,WAAW,GAAI,GAAG,GAAG,CAAC;AACxE,0BAA0BjC,KAAK,CAAC4B,WAAW,CAACC,MAAM;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB7B,KAAK,CAACkB,WAAW,CAACN,EAAE;AAC3C,eAAeZ,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AAC/B,qBAAqBrB,KAAK,CAAC0B,YAAY,CAAC,KAAK,CAAC;AAC9C;AACA;AACA,uBAAuB1B,KAAK,CAACkB,WAAW,CAACC,EAAE;AAC3C,eAAenB,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AAC/B,qBAAqBrB,KAAK,CAAC0B,YAAY,CAAC,IAAI,CAAC;AAC7C;AACA;AACA,uBAAuB1B,KAAK,CAACkB,WAAW,CAACE,EAAE;AAC3C,eAAepB,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AAC/B,qBAAqBrB,KAAK,CAAC0B,YAAY,CAACC,EAAE;AAC1C,kBAAkB3B,KAAK,CAACW,OAAO,CAACC,EAAE;AAClC;AACA,CAAC;AAAC2B,GAAA,GA7EIhB,UAAU;AA+EhB,MAAMiB,OAAO,GAAGlD,MAAM,CAAC2B,GAAG;AAC1B;AACA;AACA,mBAAmBjB,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AACnC,mBAAmBrB,KAAK,CAAC0B,YAAY,CAAC,KAAK,CAAC;AAC5C;AACA,gBAAgB1B,KAAK,CAACwB,MAAM,CAACW,SAAS,CAACM,OAAO;AAC9C;AACA;AACA;AACA;AACA,gBAAgBzC,KAAK,CAACW,OAAO,CAACQ,EAAE;AAChC,sBAAsBnB,KAAK,CAACwB,MAAM,CAACM,OAAO,CAAC,GAAG,CAAC;AAC/C,oBAAoB9B,KAAK,CAAC4B,WAAW,CAACC,MAAM;AAC5C;AACA;AACA;AACA,kBAAkB7B,KAAK,CAACW,OAAO,CAACC,EAAE;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqBZ,KAAK,CAAC0B,YAAY,CAACC,EAAE;AAC1C,4BAA4B3B,KAAK,CAAC4B,WAAW,CAACC,MAAM;AACpD;AACA;AACA;AACA;AACA,aAAa7B,KAAK,CAACwB,MAAM,CAACkB,IAAI,CAACC,QAAQ;AACvC;AACA;AACA;AACA,uBAAuB3C,KAAK,CAACkB,WAAW,CAACN,EAAE;AAC3C;AACA;AACA,qBAAqBZ,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AACrC;AACA;AACA,uBAAuBrB,KAAK,CAACkB,WAAW,CAACC,EAAE;AAC3C;AACA;AACA,qBAAqBnB,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AACrC;AACA;AACA,uBAAuBrB,KAAK,CAACkB,WAAW,CAACE,EAAE;AAC3C;AACA;AACA,qBAAqBpB,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AACrC,qBAAqBrB,KAAK,CAAC0B,YAAY,CAACd,EAAE;AAC1C;AACA,CAAC;AAACgC,GAAA,GApDIJ,OAAO;AAsDb,MAAMK,QAAQ,GAAGvD,MAAM,CAACc,OAAO,CAAC;AAChC,mBAAmBJ,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AACnC,gBAAgBrB,KAAK,CAACwB,MAAM,CAACW,SAAS,CAACG,OAAO;AAC9C;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GANID,QAAQ;AAQd,MAAME,eAAe,GAAGzD,MAAM,CAACa,IAAI,CAAC;AACpC,mBAAmBH,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AACnC;AACA;AACA;AACA;AACA,uBAAuBrB,KAAK,CAACkB,WAAW,CAACE,EAAE;AAC3C,qBAAqBpB,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AACrC;AACA,CAAC;AAAC2B,GAAA,GATID,eAAe;AAWrB,MAAME,eAAe,GAAG3D,MAAM,CAAC2B,GAAG;AAClC;AACA,SAASjB,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AACzB,WAAWrB,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AAC3B;AACA;AACA,mBAAmBrB,KAAK,CAAC0B,YAAY,CAACwB,IAAI;AAC1C;AACA;AACA;AACA,gBAAgBnB,KAAK,IAAI;EACrB,IAAIA,KAAK,CAACC,SAAS,EAAE,OAAOhC,KAAK,CAACwB,MAAM,CAACW,SAAS,CAACC,OAAO;EAC1D,IAAIL,KAAK,CAACE,WAAW,EAAE,OAAOjC,KAAK,CAACwB,MAAM,CAACW,SAAS,CAACE,KAAK;EAC1D,IAAIN,KAAK,CAACoB,WAAW,EAAE,OAAOnD,KAAK,CAACwB,MAAM,CAACW,SAAS,CAACiB,OAAO;EAC5D,OAAO,aAAa;AACtB,CAAC;AACH,WAAWpD,KAAK,CAACwB,MAAM,CAACkB,IAAI,CAACW,OAAO;AACpC,oBAAoBrD,KAAK,CAAC4B,WAAW,CAACC,MAAM;AAC5C,gBAAgBE,KAAK,IAAI;EACrB,IAAIA,KAAK,CAACC,SAAS,EAAE,OAAOhC,KAAK,CAACW,OAAO,CAACE,WAAW;EACrD,IAAIkB,KAAK,CAACE,WAAW,EAAE,OAAOjC,KAAK,CAACW,OAAO,CAAC2C,SAAS;EACrD,OAAOtD,KAAK,CAACW,OAAO,CAACQ,EAAE;AACzB,CAAC;AACH;AACA;AACA,IAAIY,KAAK,IAAIA,KAAK,CAACoB,WAAW,IAAI3D,GAAG;AACrC,iBAAiBkB,YAAY;AAC7B,GAAG;AACH;AACA,IAAIqB,KAAK,IAAIA,KAAK,CAACC,SAAS,IAAIxC,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoBQ,KAAK,CAACwB,MAAM,CAAC4B,OAAO,CAAC,GAAG,CAAC;AAC7C,uBAAuBpD,KAAK,CAAC0B,YAAY,CAACwB,IAAI;AAC9C,mBAAmBnC,OAAO;AAC1B;AACA,GAAG;AACH;AACA,uBAAuBf,KAAK,CAACkB,WAAW,CAACE,EAAE;AAC3C;AACA;AACA,WAAWpB,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AAC3B,aAAarB,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AAC7B;AACA,CAAC;AAACkC,GAAA,GAjDIN,eAAe;AAmDrB,MAAMO,WAAW,GAAGlE,MAAM,CAAC2B,GAAG;AAC9B;AACA;AACA;AACA;AACA,gBAAgBjB,KAAK,CAACwB,MAAM,CAACW,SAAS,CAACC,OAAO;AAC9C,sBAAsBpC,KAAK,CAAC4B,WAAW,CAAC6B,IAAI;AAC5C,uBAAuBzD,KAAK,CAAC0B,YAAY,CAAC,KAAK,CAAC,IAAI1B,KAAK,CAAC0B,YAAY,CAAC,KAAK,CAAC;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgC,GAAA,GApBIF,WAAW;AAsBjB,MAAMG,UAAU,GAAGrE,MAAM,CAACY,KAAK,CAAC;AAChC;AACA,SAASF,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AACzB,UAAUrB,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AAC1B;AACA;AACA,WAAWrB,KAAK,CAACwB,MAAM,CAACc,OAAO,CAAC,GAAG,CAAC;AACpC;AACA,iBAAiBtC,KAAK,CAAC4D,UAAU,CAACC,UAAU,CAACC,QAAQ;AACrD,eAAe9D,KAAK,CAAC4D,UAAU,CAACG,QAAQ,CAAC3C,EAAE;AAC3C,aAAapB,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC,IAAIrB,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AACjD,gBAAgBrB,KAAK,CAACW,OAAO,CAACS,EAAE;AAChC;AACA,uBAAuBpB,KAAK,CAACkB,WAAW,CAACE,EAAE;AAC3C,WAAWpB,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AAC3B,YAAYrB,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AAC5B,iBAAiBrB,KAAK,CAAC4D,UAAU,CAACG,QAAQ,CAACC,EAAE;AAC7C,eAAehE,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC,IAAIrB,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;AACnD;AACA,CAAC;AAAC4C,GAAA,GAnBIN,UAAU;AAqBhB,MAAMO,OAAO,GAAG3E,SAAS;AACzB;AACA;AACA,CAAC;AAED,MAAM4E,SAAS,GAAGA,CAAC;EACjBC,IAAI;EACJC,UAAU;EACVC,UAAU;EACVtC,SAAS;EACTC,WAAW;EACXkB,WAAW;EACXjB,cAAc;EACdqC,QAAQ,GAAG;AACb,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtF,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACuF,YAAY,EAAEC,eAAe,CAAC,GAAGxF,QAAQ,CAAC,CAAC,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACduF,eAAe,CAACC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACjCH,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC,EAAE,CAACN,IAAI,CAACU,GAAG,CAAC,CAAC;EAEd,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI/C,SAAS,EAAE,oBAAO1B,OAAA,CAACb,WAAW;MAACuF,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC/C,IAAInD,WAAW,EAAE,oBAAO3B,OAAA,CAACZ,SAAS;MAACsF,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC/C,IAAIjC,WAAW,EAAE,oBAAO7C,OAAA,CAACR,GAAG;MAACkF,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzC,OAAO,IAAI;EACb,CAAC;EAED,oBACE9E,OAAA,CAACU,aAAa;IAAAqE,QAAA,eACZ/E,OAAA,CAACiB,UAAU;MAETS,SAAS,EAAEA,SAAU;MACrBC,WAAW,EAAEA,WAAY;MACzBC,cAAc,EAAEA,cAAe;MAAAmD,QAAA,gBAE/B/E,OAAA,CAACqD,UAAU;QAAC2B,OAAO,EAAC,SAAS;QAAAD,QAAA,GAC1BhB,UAAU,EAAC,KAAG,EAACC,UAAU;MAAA;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eAEb9E,OAAA,CAAC2C,eAAe;QACdjB,SAAS,EAAEA,SAAU;QACrBC,WAAW,EAAEA,WAAY;QACzBkB,WAAW,EAAEA,WAAY;QAAAkC,QAAA,EAExBN,aAAa,CAAC;MAAC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAElB9E,OAAA,CAACkC,OAAO;QAAA6C,QAAA,EACL,CAACZ,QAAQ,gBACRnE,OAAA;UACEiF,GAAG,EAAEnB,IAAI,CAACoB,GAAI;UACdC,GAAG,EAAErB,IAAI,CAACsB,IAAK;UACfC,OAAO,EAAEA,CAAA,KAAMjB,WAAW,CAAC,IAAI;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,gBAEF9E,OAAA;UAAKsF,SAAS,EAAC,UAAU;UAAAP,QAAA,eACvB/E,OAAA,CAACP,QAAQ;YAACiF,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEV9E,OAAA,CAACuC,QAAQ;QAACgD,KAAK,EAAE,CAAE;QAACC,QAAQ,EAAC,SAAS;QAAAT,QAAA,EACnCjB,IAAI,CAACsB;MAAI;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEX9E,OAAA,CAACyC,eAAe;QAACiC,IAAI,EAAC,IAAI;QAACe,KAAK;QAAAV,QAAA,EAC7BjB,IAAI,CAAC4B;MAAW;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAElB9E,OAAA,CAACkD,WAAW;QAACyC,KAAK,EAAE;UAAEC,KAAK,EAAE,GAAG3B,QAAQ;QAAI;MAAE;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA,GAvC5CT,YAAY;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAwCP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEpB,CAAC;AAACZ,EAAA,CAvEIL,SAAS;AAAAgC,GAAA,GAAThC,SAAS;AAyEf,eAAeA,SAAS;AAAC,IAAA7C,EAAA,EAAAiB,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAO,GAAA,EAAAkC,GAAA;AAAAC,YAAA,CAAA9E,EAAA;AAAA8E,YAAA,CAAA7D,GAAA;AAAA6D,YAAA,CAAAxD,GAAA;AAAAwD,YAAA,CAAAtD,GAAA;AAAAsD,YAAA,CAAApD,GAAA;AAAAoD,YAAA,CAAA7C,GAAA;AAAA6C,YAAA,CAAA1C,GAAA;AAAA0C,YAAA,CAAAnC,GAAA;AAAAmC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}