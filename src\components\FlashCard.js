import React, { useState, useEffect } from 'react';
import styled, { keyframes, css } from 'styled-components';
import { CheckCircle, RotateCcw, ArrowRight, ArrowLeft, Target, Zap, Sparkles } from 'lucide-react';
import { theme } from '../styles/theme';
import { Card, Badge, Text, Heading } from './ui/ModernComponents';

// Modern Animations
const flipIn = keyframes`
  from {
    transform: perspective(600px) rotateY(-90deg);
    opacity: 0;
  }
  to {
    transform: perspective(600px) rotateY(0deg);
    opacity: 1;
  }
`;

const slideInRight = keyframes`
  from {
    transform: translateX(100%) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
`;

const slideInLeft = keyframes`
  from {
    transform: translateX(-100%) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
`;

const successPulse = keyframes`
  0% {
    transform: scale(1);
    box-shadow: ${theme.shadows.lg};
  }
  50% {
    transform: scale(1.02);
    box-shadow: ${theme.shadows.glowSuccess};
  }
  100% {
    transform: scale(1);
    box-shadow: ${theme.shadows.lg};
  }
`;

const shake = keyframes`
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-8px); }
  75% { transform: translateX(8px); }
`;

const sparkle = keyframes`
  0%, 100% {
    transform: scale(0) rotate(0deg);
    opacity: 0;
  }
  50% {
    transform: scale(1) rotate(180deg);
    opacity: 1;
  }
`;

// Modern Styled Components
const CardContainer = styled.div`
  position: relative;
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
  perspective: 1200px;

  @media (max-width: ${theme.breakpoints.lg}) {
    max-width: 420px;
  }

  @media (max-width: ${theme.breakpoints.md}) {
    max-width: 380px;
  }

  @media (max-width: ${theme.breakpoints.sm}) {
    max-width: 100%;
    padding: 0 ${theme.spacing[2]};
    margin: 0;
  }
`;

const ModernCard = styled(Card)`
  background: ${theme.colors.background};
  border-radius: ${theme.borderRadius['2xl']};
  box-shadow: ${theme.shadows.xl};
  padding: ${theme.spacing[6]};
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all ${theme.transitions.normal};
  border: 1px solid ${theme.colors.neutral[100]};
  
  @media (max-width: ${theme.breakpoints.sm}) {
    padding: ${theme.spacing[3]};
    border-radius: ${theme.borderRadius['lg']};
  }

  animation: ${props => {
    if (props.isCorrect) return css`${successPulse} 0.8s ease`;
    if (props.isIncorrect) return css`${shake} 0.6s ease`;
    if (props.slideDirection === 'right') return css`${slideInRight} 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)`;
    if (props.slideDirection === 'left') return css`${slideInLeft} 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)`;
    return css`${flipIn} 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)`;
  }};

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: ${props => {
      if (props.isCorrect) return theme.colors.gradients.success;
      if (props.isIncorrect) return theme.colors.gradients.error;
      return theme.colors.gradients.primary;
    }};
    border-radius: ${theme.borderRadius['3xl']} ${theme.borderRadius['3xl']} 0 0;
  }

  &::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: ${props => {
      if (props.isCorrect) return `radial-gradient(circle, ${theme.colors.success[100]} 0%, transparent 70%)`;
      if (props.isIncorrect) return `radial-gradient(circle, ${theme.colors.error[100]} 0%, transparent 70%)`;
      return 'transparent';
    }};
    opacity: ${props => (props.isCorrect || props.isIncorrect) ? 0.3 : 0};
    transition: opacity ${theme.transitions.normal};
    pointer-events: none;
    z-index: 0;
  }

  > * {
    position: relative;
    z-index: 1;
  }

  @media (max-width: ${theme.breakpoints.lg}) {
    padding: ${theme.spacing[6]};
    border-radius: ${theme.borderRadius['2xl']};
  }

  @media (max-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing[5]};
    border-radius: ${theme.borderRadius['xl']};
  }

  @media (max-width: ${theme.breakpoints.sm}) {
    padding: ${theme.spacing[5]};
    border-radius: ${theme.borderRadius.xl};
    box-shadow: ${theme.shadows.lg};
  }
`;

const SignGif = styled.div`
  width: 240px;
  height: 240px;
  margin: 0 auto ${theme.spacing[6]};
  border-radius: ${theme.borderRadius['2xl']};
  overflow: hidden;
  background: ${theme.colors.gradients.surface};
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: ${theme.shadows.md};
  border: 2px solid ${theme.colors.neutral[100]};
  transition: all ${theme.transitions.normal};

  &:hover {
    transform: scale(1.02);
    box-shadow: ${theme.shadows.lg};
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: ${theme.borderRadius.xl};
    transition: transform ${theme.transitions.normal};
  }

  .fallback {
    font-size: 5rem;
    color: ${theme.colors.text.tertiary};
    filter: grayscale(0.3);
  }

  @media (max-width: ${theme.breakpoints.lg}) {
    width: 220px;
    height: 220px;
    margin-bottom: ${theme.spacing[6]};
  }

  @media (max-width: ${theme.breakpoints.md}) {
    width: 200px;
    height: 200px;
    margin-bottom: ${theme.spacing[5]};
  }

  @media (max-width: ${theme.breakpoints.sm}) {
    width: 120px;
    height: 120px;
    margin-bottom: ${theme.spacing[2]};
    border-radius: ${theme.borderRadius.lg};
  }
`;

const SignName = styled(Heading)`
  margin-bottom: ${theme.spacing[2]};
  background: ${theme.colors.gradients.primary};
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
`;

const SignDescription = styled(Text)`
  margin-bottom: ${theme.spacing[6]};
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: ${theme.breakpoints.sm}) {
    margin-bottom: ${theme.spacing[4]};
  }
`;

const StatusIndicator = styled.div`
  position: absolute;
  top: ${theme.spacing[4]};
  right: ${theme.spacing[4]};
  width: 56px;
  height: 56px;
  border-radius: ${theme.borderRadius.full};
  display: flex;
  align-items: center;
  justify-content: center;
  background: ${props => {
    if (props.isCorrect) return theme.colors.gradients.success;
    if (props.isIncorrect) return theme.colors.gradients.error;
    if (props.isDetecting) return theme.colors.gradients.warning;
    return 'transparent';
  }};
  color: ${theme.colors.text.inverse};
  transition: all ${theme.transitions.normal};
  box-shadow: ${props => {
    if (props.isCorrect) return theme.shadows.glowSuccess;
    if (props.isIncorrect) return theme.shadows.glowError;
    return theme.shadows.md;
  }};
  z-index: 10;

  ${props => props.isDetecting && css`
    animation: ${successPulse} 1.5s ease infinite;
  `}

  ${props => props.isCorrect && css`
    &::before {
      content: '';
      position: absolute;
      top: -10px;
      right: -10px;
      width: 20px;
      height: 20px;
      background: ${theme.colors.warning[400]};
      border-radius: ${theme.borderRadius.full};
      animation: ${sparkle} 1s ease;
    }
  `}

  @media (max-width: ${theme.breakpoints.sm}) {
    width: 48px;
    height: 48px;
    top: ${theme.spacing[3]};
    right: ${theme.spacing[3]};
  }
`;

const ProgressBar = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  height: 6px;
  background: ${theme.colors.gradients.success};
  transition: width ${theme.transitions.slow};
  border-radius: 0 0 ${theme.borderRadius['3xl']} ${theme.borderRadius['3xl']};
  box-shadow: 0 -2px 10px rgba(34, 197, 94, 0.3);

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 2s infinite;
  }
`;

const CardNumber = styled(Badge)`
  position: absolute;
  top: ${theme.spacing[4]};
  left: ${theme.spacing[4]};
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  color: ${theme.colors.primary[700]};
  border: 1px solid rgba(255, 255, 255, 0.2);
  font-weight: ${theme.typography.fontWeight.semibold};
  font-size: ${theme.typography.fontSize.sm};
  padding: ${theme.spacing[2]} ${theme.spacing[3]};
  box-shadow: ${theme.shadows.sm};

  @media (max-width: ${theme.breakpoints.sm}) {
    top: ${theme.spacing[3]};
    left: ${theme.spacing[3]};
    font-size: ${theme.typography.fontSize.xs};
    padding: ${theme.spacing[1]} ${theme.spacing[2]};
  }
`;

const shimmer = keyframes`
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
`;

const FlashCard = ({ 
  sign, 
  cardNumber, 
  totalCards, 
  isCorrect, 
  isIncorrect, 
  isDetecting,
  slideDirection,
  progress = 0 
}) => {
  const [imgError, setImgError] = useState(false);
  const [animationKey, setAnimationKey] = useState(0);

  useEffect(() => {
    setAnimationKey(prev => prev + 1);
    setImgError(false);
  }, [sign.key]);

  const getStatusIcon = () => {
    if (isCorrect) return <CheckCircle size={20} />;
    if (isIncorrect) return <RotateCcw size={20} />;
    if (isDetecting) return <Zap size={20} />;
    return null;
  };

  return (
    <CardContainer>
      <ModernCard
        key={animationKey}
        isCorrect={isCorrect}
        isIncorrect={isIncorrect}
        slideDirection={slideDirection}
      >
        <CardNumber variant="primary">
          {cardNumber} / {totalCards}
        </CardNumber>

        <StatusIndicator
          isCorrect={isCorrect}
          isIncorrect={isIncorrect}
          isDetecting={isDetecting}
        >
          {getStatusIcon()}
        </StatusIndicator>

        <SignGif>
          {!imgError ? (
            <img
              src={sign.gif}
              alt={sign.name}
              onError={() => setImgError(true)}
            />
          ) : (
            <div className="fallback">
              <Sparkles size={60} />
            </div>
          )}
        </SignGif>

        <SignName level={2} gradient="primary">
          {sign.name}
        </SignName>

        <SignDescription size="lg" muted>
          {sign.description}
        </SignDescription>

        <ProgressBar style={{ width: `${progress}%` }} />
      </ModernCard>
    </CardContainer>
  );
};

export default FlashCard;
