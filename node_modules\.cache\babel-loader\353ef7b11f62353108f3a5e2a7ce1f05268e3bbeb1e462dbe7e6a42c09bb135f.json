{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\a11y-SL\\\\src\\\\components\\\\FlashCardTraining.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback, useEffect } from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport Webcam from 'react-webcam';\nimport { ArrowLeft, ArrowRight, RotateCcw, Home, Camera, Wifi, WifiOff, RefreshCw, CheckCircle, Trophy, Target, Zap, Sparkles, Award, AlertTriangle } from 'lucide-react';\nimport FlashCard from './FlashCard';\nimport { useSignDetection } from '../hooks/useSignDetection';\nimport { getSignsForLevel, getLevelInfo } from '../data/signLevels';\nimport { theme } from '../styles/theme';\nimport { Container, Card, Button, Heading, Text, Badge } from './ui/ModernComponents';\n\n// Animations\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst fadeIn = keyframes`\n  from { opacity: 0; transform: translateY(20px); }\n  to { opacity: 1; transform: translateY(0); }\n`;\nconst celebration = keyframes`\n  0%, 100% { transform: scale(1) rotate(0deg); }\n  25% { transform: scale(1.1) rotate(-5deg); }\n  75% { transform: scale(1.1) rotate(5deg); }\n`;\n\n// Modern Styled Components\nconst ModernContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  padding: ${theme.spacing[4]};\n  position: relative;\n  overflow-x: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n\n  > * {\n    position: relative;\n    z-index: 1;\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    padding: ${theme.spacing[3]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: ${theme.spacing[2]};\n    min-height: 100dvh; /* Use dynamic viewport height for mobile */\n  }\n`;\n_c = ModernContainer;\nconst Header = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[8]};\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    flex-direction: column;\n    gap: ${theme.spacing[4]};\n  }\n`;\nconst ModernHeader = styled(Card)`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[6]};\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: ${theme.shadows.xl};\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    flex-direction: column;\n    gap: ${theme.spacing[4]};\n    text-align: center;\n    margin-bottom: ${theme.spacing[5]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    margin-bottom: ${theme.spacing[4]};\n    padding: ${theme.spacing[3]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    margin-bottom: ${theme.spacing[4]};\n  }\n`;\n_c2 = ModernHeader;\nconst ModernBackButton = styled(Button)`\n  background: rgba(59, 130, 246, 0.15);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(59, 130, 246, 0.3);\n  color: #3b82f6;\n  font-weight: 600;\n  transition: all 0.3s ease;\n\n  &:hover:not(:disabled) {\n    background: rgba(59, 130, 246, 0.25);\n    border-color: rgba(59, 130, 246, 0.5);\n    color: #2563eb;\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);\n  }\n\n  &:active:not(:disabled) {\n    transform: translateY(0);\n    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    width: 100%;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: 0.75rem 1rem;\n    font-size: 0.875rem;\n  }\n`;\n_c3 = ModernBackButton;\nconst ModernLevelInfo = styled.div`\n  text-align: center;\n  color: ${theme.colors.text.primary};\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    order: -1;\n  }\n`;\n_c4 = ModernLevelInfo;\nconst ModernLevelTitle = styled(Heading)`\n  margin-bottom: ${theme.spacing[1]};\n  background: ${theme.colors.gradients.primary};\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n`;\n_c5 = ModernLevelTitle;\nconst ModernLevelTheme = styled(Text)`\n  opacity: 0.8;\n  font-weight: ${theme.typography.fontWeight.medium};\n`;\n_c6 = ModernLevelTheme;\nconst ModernConnectionStatus = styled(Badge)`\n  background: ${props => props.isConnected ? 'rgba(34, 197, 94, 0.15)' : 'rgba(239, 68, 68, 0.15)'};\n  color: ${props => props.isConnected ? theme.colors.success[700] : theme.colors.error[700]};\n  border: 1px solid ${props => props.isConnected ? theme.colors.success[200] : theme.colors.error[200]};\n  backdrop-filter: blur(10px);\n  cursor: ${props => props.isConnected ? 'default' : 'pointer'};\n\n  &:hover {\n    background: ${props => props.isConnected ? 'rgba(34, 197, 94, 0.2)' : 'rgba(239, 68, 68, 0.2)'};\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    width: 100%;\n    justify-content: center;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: 0.5rem 0.75rem;\n    font-size: 0.75rem;\n    \n    .connection-text {\n      display: none; /* Hide text on mobile, show only icon */\n    }\n  }\n`;\n_c7 = ModernConnectionStatus;\nconst MainContent = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 500px;\n  gap: ${theme.spacing[6]};\n  max-width: 1400px;\n  margin: 0 auto;\n  align-items: start;\n\n  @media (max-width: ${theme.breakpoints.xl}) {\n    grid-template-columns: 1fr 450px;\n    gap: ${theme.spacing[5]};\n    max-width: 1200px;\n  }\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    grid-template-columns: 1fr;\n    gap: ${theme.spacing[6]};\n    max-width: 800px;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    /* On mobile, show side by side for simultaneous viewing */\n    display: grid;\n    grid-template-columns: 1fr 1fr;\n    gap: ${theme.spacing[2]};\n    max-width: 100%;\n    height: 100vh;\n    padding: ${theme.spacing[2]};\n  }\n`;\n_c8 = MainContent;\nconst FlashCardSection = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  animation: ${fadeIn} 0.6s ease;\n  position: sticky;\n  top: ${theme.spacing[8]};\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    position: static;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    /* Mobile: optimize for side-by-side viewing */\n    height: 100%;\n    overflow-y: auto;\n    padding: ${theme.spacing[1]};\n    gap: ${theme.spacing[2]};\n  }\n`;\n_c9 = FlashCardSection;\nconst ProgressSection = styled.div`\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20px;\n  padding: 1rem;\n  margin-bottom: 1.5rem;\n  text-align: center;\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  width: 100%;\n  max-width: 480px;\n  margin-left: auto;\n  margin-right: auto;\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    max-width: 420px;\n    padding: 0.875rem;\n    margin-bottom: 1.5rem;\n    border-radius: 18px;\n  }\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    max-width: 380px;\n    padding: 0.75rem;\n    margin-bottom: 1.5rem;\n    border-radius: 16px;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    max-width: 100%;\n    padding: ${theme.spacing[2]};\n    margin-bottom: ${theme.spacing[2]};\n    border-radius: 12px;\n  }\n`;\n_c0 = ProgressSection;\nconst ProgressTitle = styled.h3`\n  font-size: 1.125rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin-bottom: 0.75rem;\n`;\n_c1 = ProgressTitle;\nconst ProgressBar = styled.div`\n  width: 100%;\n  height: 14px;\n  background: #e2e8f0;\n  border-radius: 8px;\n  overflow: hidden;\n  margin-bottom: 0.75rem;\n  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    height: 12px;\n    margin-bottom: 0.75rem;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    height: 10px;\n    margin-bottom: 0.75rem;\n  }\n`;\n_c10 = ProgressBar;\nconst ProgressFill = styled.div`\n  height: 100%;\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #10b981);\n  border-radius: 8px;\n  transition: width 0.5s ease;\n  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    border-radius: 7px;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    border-radius: 6px;\n  }\n`;\n_c11 = ProgressFill;\nconst ProgressText = styled.div`\n  font-size: 0.875rem;\n  color: #64748b;\n  font-weight: 600;\n`;\n_c12 = ProgressText;\nconst Controls = styled.div`\n  display: flex;\n  gap: 1rem;\n  margin-top: 2rem;\n  justify-content: center;\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    gap: 0.75rem;\n    margin-top: 1.5rem;\n  }\n  \n  @media (max-width: 768px) {\n    gap: 0.75rem;\n    margin-top: 1.5rem;\n    flex-wrap: wrap;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    gap: ${theme.spacing[1]};\n    margin-top: ${theme.spacing[2]};\n    padding: 0;\n    flex-direction: row;\n    flex-wrap: wrap;\n  }\n`;\n_c13 = Controls;\nconst ControlButton = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 1rem 2rem;\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-height: 48px; /* Minimum touch target size */\n  touch-action: manipulation; /* Optimize for touch */\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    padding: 1rem 2rem;\n    font-size: 1rem;\n    min-height: 48px;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: 0.5rem 1rem;\n    font-size: 0.75rem;\n    min-height: 36px;\n    flex: 1;\n    min-width: 80px;\n  }\n  \n  ${props => {\n  if (props.variant === 'primary') {\n    return `\n        background: linear-gradient(135deg, #3b82f6, #8b5cf6);\n        color: white;\n        &:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);\n        }\n      `;\n  }\n  if (props.variant === 'success') {\n    return `\n        background: linear-gradient(135deg, #10b981, #34d399);\n        color: white;\n        &:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);\n        }\n      `;\n  }\n  return `\n      background: rgba(255, 255, 255, 0.9);\n      color: #64748b;\n      &:hover {\n        background: white;\n        transform: translateY(-2px);\n      }\n    `;\n}}\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none !important;\n  }\n  \n  @media (max-width: 768px) {\n    padding: 0.75rem 1.5rem;\n    font-size: 0.875rem;\n  }\n`;\n_c14 = ControlButton;\nconst CameraSection = styled(Card)`\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n  border-radius: 20px;\n  padding: 1.5rem;\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    order: 1; /* Show after flash card on mobile */\n    padding: 1.25rem;\n    border-radius: 18px;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    /* Mobile: optimize for side-by-side viewing */\n    order: 0; /* Reset order for side-by-side layout */\n    margin: 0;\n    border-radius: 12px;\n    padding: ${theme.spacing[2]};\n    height: 100%;\n    overflow-y: auto;\n    display: flex;\n    flex-direction: column;\n  }\n`;\n_c15 = CameraSection;\nconst CameraTitle = styled.h3`\n  font-size: 1.25rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin-bottom: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    font-size: 1.125rem;\n    margin-bottom: 0.75rem;\n    gap: 0.5rem;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    font-size: 0.875rem;\n    margin-bottom: ${theme.spacing[1]};\n    gap: 0.25rem;\n  }\n`;\n_c16 = CameraTitle;\nconst WebcamContainer = styled.div`\n  position: relative;\n  border-radius: 18px;\n  overflow: hidden;\n  background: #000;\n  margin-bottom: 1rem;\n  aspect-ratio: 4/3; /* Maintain aspect ratio */\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n  border: 2px solid rgba(255, 255, 255, 0.1);\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    border-radius: 16px;\n    margin-bottom: 0.75rem;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    border-radius: 8px;\n    margin-bottom: ${theme.spacing[2]};\n    aspect-ratio: 3/2; /* More compact ratio for mobile side-by-side */\n    flex-shrink: 0;\n  }\n`;\n_c17 = WebcamContainer;\nconst WebcamError = styled.div`\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: #f8fafc;\n  color: #64748b;\n  text-align: center;\n  padding: 1rem;\n  \n  svg {\n    width: 48px;\n    height: 48px;\n    margin-bottom: 0.75rem;\n    color: #94a3b8;\n  }\n`;\n_c18 = WebcamError;\nconst StatusOverlay = styled.div`\n  position: absolute;\n  top: 1rem;\n  left: 1rem;\n  right: 1rem;\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 0.75rem;\n  border-radius: 8px;\n  font-weight: 600;\n  text-align: center;\n  font-size: 0.875rem;\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    top: 0.5rem;\n    left: 0.5rem;\n    right: 0.5rem;\n    padding: 0.5rem;\n    font-size: 0.75rem;\n  }\n`;\n_c19 = StatusOverlay;\nconst PredictionDisplay = styled.div`\n  background: linear-gradient(135deg, #f8fafc, #f1f5f9);\n  border-radius: 14px;\n  padding: 1.25rem;\n  text-align: center;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    padding: 1rem;\n    border-radius: 12px;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: ${theme.spacing[2]};\n    margin: 0;\n    border-radius: 8px;\n    flex-shrink: 0;\n  }\n`;\n_c20 = PredictionDisplay;\nconst PredictionText = styled.div`\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin-bottom: 0.75rem;\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    font-size: 1.125rem;\n    margin-bottom: 0.5rem;\n  }\n`;\n_c21 = PredictionText;\nconst ConfidenceText = styled.div`\n  font-size: 0.875rem;\n  color: #64748b;\n`;\n_c22 = ConfidenceText;\nconst CompletionModal = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  animation: ${fadeIn} 0.3s ease;\n`;\n_c23 = CompletionModal;\nconst ModalContent = styled.div`\n  background: white;\n  border-radius: 24px;\n  padding: 3rem;\n  text-align: center;\n  max-width: 500px;\n  margin: 1rem;\n  animation: ${celebration} 0.6s ease;\n`;\n_c24 = ModalContent;\nconst ModalTitle = styled.h2`\n  font-size: 2.5rem;\n  font-weight: 800;\n  color: #1e293b;\n  margin-bottom: 1rem;\n`;\n_c25 = ModalTitle;\nconst ModalText = styled.p`\n  font-size: 1.125rem;\n  color: #64748b;\n  margin-bottom: 2rem;\n  line-height: 1.6;\n`;\n_c26 = ModalText;\nconst ModalButtons = styled.div`\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n`;\n_c27 = ModalButtons;\nconst FlashCardTraining = ({\n  level,\n  onBack,\n  userProgress = {},\n  onProgressUpdate\n}) => {\n  _s();\n  const webcamRef = useRef(null);\n  const [currentCardIndex, setCurrentCardIndex] = useState(0);\n  const [completedCards, setCompletedCards] = useState(new Set());\n  const [cardStates, setCardStates] = useState({});\n  const [slideDirection, setSlideDirection] = useState(null);\n  const [showCompletion, setShowCompletion] = useState(false);\n  const [isCapturing, setIsCapturing] = useState(false);\n  const [webcamError, setWebcamError] = useState(false);\n  const [webcamLoading, setWebcamLoading] = useState(true);\n  const [constraintAttempt, setConstraintAttempt] = useState(0);\n  const levelInfo = getLevelInfo(level);\n  const signs = getSignsForLevel(level);\n  const currentSign = signs[currentCardIndex];\n\n  // Use sign detection hook\n  const {\n    isConnected,\n    prediction,\n    isAIRecording,\n    recordingStatus,\n    signMatched,\n    targetSign,\n    currentKeypoints,\n    startRecording: startAIRecording,\n    stopRecording: stopAIRecording,\n    startFrameCapture,\n    retryConnection,\n    setLevel\n  } = useSignDetection();\n  const progress = completedCards.size / signs.length * 100;\n\n  // Webcam error handling\n  const handleWebcamError = useCallback(error => {\n    console.error('Webcam error:', error);\n\n    // Try different constraint configurations\n    if (constraintAttempt < 3) {\n      setConstraintAttempt(prev => prev + 1);\n      setWebcamLoading(true);\n      return;\n    }\n    setWebcamError(true);\n    setWebcamLoading(false);\n  }, [constraintAttempt]);\n  const handleWebcamLoad = useCallback(() => {\n    setWebcamError(false);\n    setWebcamLoading(false);\n  }, []);\n  const retryWebcam = useCallback(() => {\n    setWebcamError(false);\n    setWebcamLoading(true);\n    setConstraintAttempt(0);\n  }, []);\n\n  // Chrome-compatible video constraints with fallback\n  const getVideoConstraints = useCallback(() => {\n    const constraints = [\n    // First attempt: Full constraints\n    {\n      width: {\n        ideal: 640,\n        min: 320\n      },\n      height: {\n        ideal: 480,\n        min: 240\n      },\n      facingMode: \"user\",\n      aspectRatio: {\n        ideal: 4 / 3\n      }\n    },\n    // Second attempt: Simplified for Chrome\n    {\n      width: {\n        ideal: 640\n      },\n      height: {\n        ideal: 480\n      },\n      facingMode: \"user\"\n    },\n    // Third attempt: Minimal constraints\n    {\n      facingMode: \"user\"\n    },\n    // Fourth attempt: No constraints\n    {}];\n    return constraints[constraintAttempt] || constraints[constraints.length - 1];\n  }, [constraintAttempt]);\n\n  // Start detection when connected\n  const startDetection = useCallback(() => {\n    if (!webcamRef.current) return;\n    setIsCapturing(true);\n    startFrameCapture(webcamRef, 100);\n  }, [startFrameCapture]);\n\n  // Save training data when sign is detected correctly\n  const saveTrainingData = useCallback(async (signName, keypoints, confidence) => {\n    try {\n      console.log(`💾 Saving training data for ${signName} with confidence ${confidence}`);\n      const response = await fetch('http://localhost:8000/save-training-data', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          sign_name: signName,\n          keypoints: keypoints,\n          confidence: confidence,\n          timestamp: new Date().toISOString()\n        })\n      });\n      if (response.ok) {\n        const result = await response.json();\n        console.log(`✅ Training data saved: ${result.message}`);\n        return true;\n      } else {\n        console.error('❌ Failed to save training data');\n        return false;\n      }\n    } catch (error) {\n      console.error('❌ Error saving training data:', error);\n      return false;\n    }\n  }, []);\n  const nextCard = useCallback(() => {\n    if (currentCardIndex < signs.length - 1) {\n      setSlideDirection('right');\n      setCurrentCardIndex(prev => prev + 1);\n      setCardStates(prev => ({\n        ...prev,\n        [currentCardIndex]: null\n      }));\n      setTimeout(() => setSlideDirection(null), 500);\n    }\n  }, [currentCardIndex, signs.length]);\n\n  // Handle sign detection success with automatic recording and training data saving\n  useEffect(() => {\n    var _prediction$sign;\n    console.log(`🔍 Debug: signMatched=${signMatched}, currentSign=${currentSign === null || currentSign === void 0 ? void 0 : currentSign.name}, prediction=${prediction === null || prediction === void 0 ? void 0 : prediction.sign}, confidence=${prediction === null || prediction === void 0 ? void 0 : prediction.confidence}`);\n    if (signMatched && currentSign && (prediction === null || prediction === void 0 ? void 0 : (_prediction$sign = prediction.sign) === null || _prediction$sign === void 0 ? void 0 : _prediction$sign.toLowerCase()) === currentSign.name.toLowerCase()) {\n      console.log(`✅ Sign match confirmed: ${currentSign.name} with confidence ${prediction.confidence}`);\n\n      // Only proceed if this card hasn't been completed yet\n      if (!completedCards.has(currentCardIndex)) {\n        console.log(`🎯 Correct sign detected: ${currentSign.name} with confidence ${prediction.confidence}`);\n\n        // Save training data immediately when sign is detected correctly\n        const saveTrainingDataAsync = async () => {\n          if (currentKeypoints && (prediction === null || prediction === void 0 ? void 0 : prediction.confidence) >= 0.5) {\n            const saved = await saveTrainingData(currentSign.name, currentKeypoints, prediction.confidence);\n            if (saved) {\n              console.log(`✅ Training data saved successfully for ${currentSign.name}`);\n            }\n          }\n        };\n\n        // Save training data\n        saveTrainingDataAsync();\n\n        // Start automatic recording for additional training data\n        if (!isAIRecording && isConnected) {\n          console.log(`🎬 Starting automatic recording for ${currentSign.name}...`);\n          startAIRecording(currentSign.name, true); // Start immediate recording session\n\n          // Stop recording after 3 seconds\n          setTimeout(() => {\n            stopAIRecording();\n            console.log(`✅ Automatic recording completed for: ${currentSign.name}`);\n          }, 3000);\n        }\n\n        // Mark card as completed\n        setCardStates(prev => ({\n          ...prev,\n          [currentCardIndex]: 'correct'\n        }));\n        setCompletedCards(prev => new Set([...prev, currentCardIndex]));\n\n        // Update progress\n        if (onProgressUpdate) {\n          const newCompletedCount = completedCards.size + 1;\n          onProgressUpdate(level, newCompletedCount, signs.length);\n        }\n\n        // Auto-advance after 2 seconds (allowing time for user to see success)\n        setTimeout(() => {\n          if (currentCardIndex < signs.length - 1) {\n            nextCard();\n          } else {\n            // Level completed\n            setShowCompletion(true);\n            if (onProgressUpdate) {\n              onProgressUpdate(level, signs.length, signs.length);\n            }\n          }\n        }, 2000);\n      } else {\n        console.log(`⚠️ Card ${currentCardIndex} already completed`);\n      }\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [signMatched, currentSign, prediction, currentCardIndex, signs.length, level, onProgressUpdate, isAIRecording, isConnected, startAIRecording, stopAIRecording, completedCards, saveTrainingData, currentKeypoints]);\n\n  // Set level when connected\n  useEffect(() => {\n    if (isConnected && level) {\n      setLevel(level);\n    }\n  }, [isConnected, level, setLevel]);\n\n  // Auto-start detection when connected\n  useEffect(() => {\n    if (isConnected && webcamRef.current && !isCapturing) {\n      startDetection();\n    }\n  }, [isConnected, startDetection, isCapturing]);\n\n  // Set target sign when current sign changes\n  useEffect(() => {\n    if (isConnected && currentSign) {\n      console.log(`🎯 Setting target sign to: ${currentSign.name}`);\n      startAIRecording(currentSign.name, false); // Set target sign without starting session\n    }\n  }, [isConnected, currentSign, startAIRecording]);\n  const prevCard = useCallback(() => {\n    if (currentCardIndex > 0) {\n      setSlideDirection('left');\n      setCurrentCardIndex(prev => prev - 1);\n      setCardStates(prev => ({\n        ...prev,\n        [currentCardIndex]: null\n      }));\n      setTimeout(() => setSlideDirection(null), 500);\n    }\n  }, [currentCardIndex]);\n  const retryCard = useCallback(() => {\n    setCardStates(prev => ({\n      ...prev,\n      [currentCardIndex]: null\n    }));\n    setCompletedCards(prev => {\n      const newSet = new Set(prev);\n      newSet.delete(currentCardIndex);\n      return newSet;\n    });\n  }, [currentCardIndex]);\n  const handleLevelComplete = () => {\n    setShowCompletion(false);\n    onBack();\n  };\n  const handleNextLevel = () => {\n    setShowCompletion(false);\n    // This would typically navigate to the next level\n    onBack();\n  };\n  if (!levelInfo || !currentSign) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: 'white',\n          textAlign: 'center',\n          padding: '2rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Level not found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 894,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          children: \"Go Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 895,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 893,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 892,\n      columnNumber: 7\n    }, this);\n  }\n  const isCurrentCardCompleted = completedCards.has(currentCardIndex);\n  const currentCardState = cardStates[currentCardIndex];\n  return /*#__PURE__*/_jsxDEV(ModernContainer, {\n    children: [/*#__PURE__*/_jsxDEV(ModernHeader, {\n      size: \"md\",\n      children: [/*#__PURE__*/_jsxDEV(ModernBackButton, {\n        variant: \"ghost\",\n        size: \"md\",\n        onClick: onBack,\n        children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 908,\n          columnNumber: 11\n        }, this), \"Back to Levels\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 907,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ModernLevelInfo, {\n        children: [/*#__PURE__*/_jsxDEV(ModernLevelTitle, {\n          level: 3,\n          children: [\"Level \", level, \": \", levelInfo.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 913,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModernLevelTheme, {\n          size: \"lg\",\n          children: levelInfo.theme\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 916,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 912,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ModernConnectionStatus, {\n        isConnected: isConnected,\n        onClick: !isConnected ? retryConnection : undefined,\n        children: [isConnected ? /*#__PURE__*/_jsxDEV(Wifi, {\n          size: 18\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 925,\n          columnNumber: 26\n        }, this) : /*#__PURE__*/_jsxDEV(WifiOff, {\n          size: 18\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 925,\n          columnNumber: 47\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"connection-text\",\n          children: isConnected ? 'Connected' : 'Disconnected'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 926,\n          columnNumber: 11\n        }, this), !isConnected && /*#__PURE__*/_jsxDEV(RefreshCw, {\n          size: 14\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 929,\n          columnNumber: 28\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 921,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 906,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: [/*#__PURE__*/_jsxDEV(FlashCardSection, {\n        children: [/*#__PURE__*/_jsxDEV(ProgressSection, {\n          children: [/*#__PURE__*/_jsxDEV(ProgressTitle, {\n            children: \"Level Progress\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 936,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            children: /*#__PURE__*/_jsxDEV(ProgressFill, {\n              style: {\n                width: `${progress}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 938,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 937,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressText, {\n            children: [completedCards.size, \" of \", signs.length, \" signs completed (\", Math.round(progress), \"%)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 940,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 935,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FlashCard, {\n          sign: currentSign,\n          cardNumber: currentCardIndex + 1,\n          totalCards: signs.length,\n          isCorrect: currentCardState === 'correct',\n          isIncorrect: currentCardState === 'incorrect',\n          isDetecting: isConnected && !isCurrentCardCompleted,\n          slideDirection: slideDirection,\n          progress: currentCardIndex / signs.length * 100\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 945,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Controls, {\n          children: [/*#__PURE__*/_jsxDEV(ControlButton, {\n            onClick: prevCard,\n            disabled: currentCardIndex === 0,\n            children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 961,\n              columnNumber: 15\n            }, this), \"Previous\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 957,\n            columnNumber: 13\n          }, this), isCurrentCardCompleted ? /*#__PURE__*/_jsxDEV(ControlButton, {\n            variant: \"success\",\n            onClick: nextCard,\n            disabled: currentCardIndex === signs.length - 1,\n            children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 971,\n              columnNumber: 17\n            }, this), \"Next Card\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 966,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(ControlButton, {\n            onClick: retryCard,\n            disabled: !isConnected,\n            children: [/*#__PURE__*/_jsxDEV(RotateCcw, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 979,\n              columnNumber: 17\n            }, this), \"Retry\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 975,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ControlButton, {\n            onClick: nextCard,\n            disabled: currentCardIndex === signs.length - 1,\n            children: [\"Next\", /*#__PURE__*/_jsxDEV(ArrowRight, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 989,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 984,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 956,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 934,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CameraSection, {\n        children: [/*#__PURE__*/_jsxDEV(CameraTitle, {\n          children: [/*#__PURE__*/_jsxDEV(Camera, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 996,\n            columnNumber: 13\n          }, this), \"Camera Feed\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 995,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(WebcamContainer, {\n          children: webcamError ? /*#__PURE__*/_jsxDEV(WebcamError, {\n            children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1003,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '1.1rem',\n                fontWeight: '600',\n                marginBottom: '8px'\n              },\n              children: \"Camera Access Error\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1004,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.9rem',\n                marginBottom: '16px',\n                lineHeight: '1.4'\n              },\n              children: \"Chrome requires camera permissions. Please:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1007,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.85rem',\n                textAlign: 'left',\n                lineHeight: '1.5'\n              },\n              children: [\"1. Click the camera icon in the address bar\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1011,\n                columnNumber: 62\n              }, this), \"2. Select \\\"Allow\\\" for camera access\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1012,\n                columnNumber: 54\n              }, this), \"3. Refresh the page\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1010,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: retryWebcam,\n              style: {\n                marginTop: '16px',\n                padding: '8px 16px',\n                background: '#3b82f6',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                cursor: 'pointer',\n                fontSize: '0.9rem',\n                fontWeight: '500'\n              },\n              children: \"Retry Camera\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1015,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1002,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Webcam, {\n              ref: webcamRef,\n              audio: false,\n              width: \"100%\",\n              height: \"auto\",\n              screenshotFormat: \"image/jpeg\",\n              videoConstraints: getVideoConstraints(),\n              onUserMediaError: handleWebcamError,\n              onUserMedia: handleWebcamLoad,\n              forceScreenshotSourceSize: true\n            }, `webcam-${constraintAttempt}`, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1034,\n              columnNumber: 17\n            }, this), webcamLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: '50%',\n                left: '50%',\n                transform: 'translate(-50%, -50%)',\n                background: 'rgba(0,0,0,0.7)',\n                color: 'white',\n                padding: '12px 20px',\n                borderRadius: '8px',\n                fontSize: '0.9rem',\n                zIndex: 10\n              },\n              children: \"Loading camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1048,\n              columnNumber: 19\n            }, this), recordingStatus && /*#__PURE__*/_jsxDEV(StatusOverlay, {\n              children: recordingStatus\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1065,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1000,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PredictionDisplay, {\n          children: [/*#__PURE__*/_jsxDEV(PredictionText, {\n            children: prediction !== null && prediction !== void 0 && prediction.sign ? `Detected: ${prediction.sign}` : 'Show the sign to get started'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1074,\n            columnNumber: 13\n          }, this), (prediction === null || prediction === void 0 ? void 0 : prediction.confidence) && /*#__PURE__*/_jsxDEV(ConfidenceText, {\n            children: [\"Confidence: \", Math.round(prediction.confidence * 100), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1078,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1073,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 994,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 933,\n      columnNumber: 7\n    }, this), showCompletion && /*#__PURE__*/_jsxDEV(CompletionModal, {\n      children: /*#__PURE__*/_jsxDEV(ModalContent, {\n        children: [/*#__PURE__*/_jsxDEV(Trophy, {\n          size: 80,\n          style: {\n            color: '#f59e0b',\n            marginBottom: '1rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1089,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ModalTitle, {\n          children: \"\\uD83C\\uDF89 Level Complete!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1090,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ModalText, {\n          children: [\"Congratulations! You've successfully completed Level \", level, \": \", levelInfo.name, \". You've mastered all \", signs.length, \" signs in this level!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1091,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ModalButtons, {\n          children: [/*#__PURE__*/_jsxDEV(ControlButton, {\n            onClick: handleLevelComplete,\n            children: [/*#__PURE__*/_jsxDEV(Home, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1097,\n              columnNumber: 17\n            }, this), \"Back to Levels\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1096,\n            columnNumber: 15\n          }, this), level < 5 && /*#__PURE__*/_jsxDEV(ControlButton, {\n            variant: \"primary\",\n            onClick: handleNextLevel,\n            children: [/*#__PURE__*/_jsxDEV(Target, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1102,\n              columnNumber: 19\n            }, this), \"Next Level\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1101,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1095,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1088,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1087,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 905,\n    columnNumber: 5\n  }, this);\n};\n_s(FlashCardTraining, \"YIsdOSFbcH4F/n8x+WhWNznKjBU=\", false, function () {\n  return [useSignDetection];\n});\n_c28 = FlashCardTraining;\nexport default FlashCardTraining;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28;\n$RefreshReg$(_c, \"ModernContainer\");\n$RefreshReg$(_c2, \"ModernHeader\");\n$RefreshReg$(_c3, \"ModernBackButton\");\n$RefreshReg$(_c4, \"ModernLevelInfo\");\n$RefreshReg$(_c5, \"ModernLevelTitle\");\n$RefreshReg$(_c6, \"ModernLevelTheme\");\n$RefreshReg$(_c7, \"ModernConnectionStatus\");\n$RefreshReg$(_c8, \"MainContent\");\n$RefreshReg$(_c9, \"FlashCardSection\");\n$RefreshReg$(_c0, \"ProgressSection\");\n$RefreshReg$(_c1, \"ProgressTitle\");\n$RefreshReg$(_c10, \"ProgressBar\");\n$RefreshReg$(_c11, \"ProgressFill\");\n$RefreshReg$(_c12, \"ProgressText\");\n$RefreshReg$(_c13, \"Controls\");\n$RefreshReg$(_c14, \"ControlButton\");\n$RefreshReg$(_c15, \"CameraSection\");\n$RefreshReg$(_c16, \"CameraTitle\");\n$RefreshReg$(_c17, \"WebcamContainer\");\n$RefreshReg$(_c18, \"WebcamError\");\n$RefreshReg$(_c19, \"StatusOverlay\");\n$RefreshReg$(_c20, \"PredictionDisplay\");\n$RefreshReg$(_c21, \"PredictionText\");\n$RefreshReg$(_c22, \"ConfidenceText\");\n$RefreshReg$(_c23, \"CompletionModal\");\n$RefreshReg$(_c24, \"ModalContent\");\n$RefreshReg$(_c25, \"ModalTitle\");\n$RefreshReg$(_c26, \"ModalText\");\n$RefreshReg$(_c27, \"ModalButtons\");\n$RefreshReg$(_c28, \"FlashCardTraining\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "useEffect", "styled", "keyframes", "Webcam", "ArrowLeft", "ArrowRight", "RotateCcw", "Home", "Camera", "Wifi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RefreshCw", "CheckCircle", "Trophy", "Target", "Zap", "<PERSON><PERSON><PERSON>", "Award", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FlashCard", "useSignDetection", "getSignsForLevel", "getLevelInfo", "theme", "Container", "Card", "<PERSON><PERSON>", "Heading", "Text", "Badge", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "fadeIn", "celebration", "ModernContainer", "div", "spacing", "breakpoints", "md", "sm", "_c", "Header", "ModernHeader", "shadows", "xl", "_c2", "ModernBackButton", "_c3", "ModernLevelInfo", "colors", "text", "primary", "_c4", "ModernLevelTitle", "gradients", "_c5", "ModernLevelTheme", "typography", "fontWeight", "medium", "_c6", "ModernConnectionStatus", "props", "isConnected", "success", "error", "_c7", "MainContent", "lg", "_c8", "FlashCardSection", "_c9", "ProgressSection", "_c0", "ProgressTitle", "h3", "_c1", "ProgressBar", "_c10", "ProgressFill", "_c11", "ProgressText", "_c12", "Controls", "_c13", "ControlButton", "button", "variant", "_c14", "CameraSection", "_c15", "CameraTitle", "_c16", "WebcamContainer", "_c17", "WebcamError", "_c18", "StatusOverlay", "_c19", "PredictionDisplay", "_c20", "PredictionText", "_c21", "ConfidenceText", "_c22", "CompletionModal", "_c23", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c24", "ModalTitle", "h2", "_c25", "ModalText", "p", "_c26", "ModalButtons", "_c27", "FlashCardTraining", "level", "onBack", "userProgress", "onProgressUpdate", "_s", "webcamRef", "currentCardIndex", "setCurrentCardIndex", "completedCards", "setCompletedCards", "Set", "cardStates", "setCardStates", "slideDirection", "setSlideDirection", "showCompletion", "setShowCompletion", "isCapturing", "setIsCapturing", "webcamError", "setWebcamError", "webcamLoading", "setWebcamLoading", "constraintAttempt", "setConstraintAttempt", "levelInfo", "signs", "currentSign", "prediction", "isAIRecording", "recordingStatus", "signMatched", "targetSign", "currentKeypoints", "startRecording", "startAIRecording", "stopRecording", "stopAIRecording", "startFrameCapture", "retryConnection", "setLevel", "progress", "size", "length", "handleWebcamError", "console", "prev", "handleWebcamLoad", "retryWebcam", "getVideoConstraints", "constraints", "width", "ideal", "min", "height", "facingMode", "aspectRatio", "startDetection", "current", "saveTrainingData", "signName", "keypoints", "confidence", "log", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "sign_name", "timestamp", "Date", "toISOString", "ok", "result", "json", "message", "nextCard", "setTimeout", "_prediction$sign", "name", "sign", "toLowerCase", "has", "saveTrainingDataAsync", "saved", "newCompletedCount", "prevCard", "retryCard", "newSet", "delete", "handleLevelComplete", "handleNextLevel", "children", "style", "color", "textAlign", "padding", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "isCurrentCardCompleted", "currentCardState", "undefined", "className", "Math", "round", "cardNumber", "totalCards", "isCorrect", "isIncorrect", "isDetecting", "disabled", "fontSize", "marginBottom", "lineHeight", "marginTop", "background", "border", "borderRadius", "cursor", "ref", "audio", "screenshotFormat", "videoConstraints", "onUserMediaError", "onUserMedia", "forceScreenshotSourceSize", "position", "top", "left", "transform", "zIndex", "_c28", "$RefreshReg$"], "sources": ["D:/ASL/a11y-SL/src/components/FlashCardTraining.js"], "sourcesContent": ["import React, { useState, useRef, useCallback, useEffect } from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport Webcam from 'react-webcam';\nimport {\n  ArrowLeft,\n  ArrowRight,\n  RotateCcw,\n  Home,\n  Camera,\n  Wifi,\n  WifiOff,\n  RefreshCw,\n  CheckCircle,\n  Trophy,\n  Target,\n  Zap,\n  Sparkles,\n  Award,\n  AlertTriangle\n} from 'lucide-react';\nimport FlashCard from './FlashCard';\nimport { useSignDetection } from '../hooks/useSignDetection';\nimport { getSignsForLevel, getLevelInfo } from '../data/signLevels';\nimport { theme } from '../styles/theme';\nimport { Container, Card, Button, Heading, Text, Badge } from './ui/ModernComponents';\n\n// Animations\nconst fadeIn = keyframes`\n  from { opacity: 0; transform: translateY(20px); }\n  to { opacity: 1; transform: translateY(0); }\n`;\n\nconst celebration = keyframes`\n  0%, 100% { transform: scale(1) rotate(0deg); }\n  25% { transform: scale(1.1) rotate(-5deg); }\n  75% { transform: scale(1.1) rotate(5deg); }\n`;\n\n// Modern Styled Components\nconst ModernContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  padding: ${theme.spacing[4]};\n  position: relative;\n  overflow-x: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n\n  > * {\n    position: relative;\n    z-index: 1;\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    padding: ${theme.spacing[3]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: ${theme.spacing[2]};\n    min-height: 100dvh; /* Use dynamic viewport height for mobile */\n  }\n`;\nconst Header = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[8]};\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    flex-direction: column;\n    gap: ${theme.spacing[4]};\n  }\n`;\n\nconst ModernHeader = styled(Card)`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[6]};\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: ${theme.shadows.xl};\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    flex-direction: column;\n    gap: ${theme.spacing[4]};\n    text-align: center;\n    margin-bottom: ${theme.spacing[5]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    margin-bottom: ${theme.spacing[4]};\n    padding: ${theme.spacing[3]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    margin-bottom: ${theme.spacing[4]};\n  }\n`;\n\nconst ModernBackButton = styled(Button)`\n  background: rgba(59, 130, 246, 0.15);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(59, 130, 246, 0.3);\n  color: #3b82f6;\n  font-weight: 600;\n  transition: all 0.3s ease;\n\n  &:hover:not(:disabled) {\n    background: rgba(59, 130, 246, 0.25);\n    border-color: rgba(59, 130, 246, 0.5);\n    color: #2563eb;\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);\n  }\n\n  &:active:not(:disabled) {\n    transform: translateY(0);\n    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    width: 100%;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: 0.75rem 1rem;\n    font-size: 0.875rem;\n  }\n`;\n\nconst ModernLevelInfo = styled.div`\n  text-align: center;\n  color: ${theme.colors.text.primary};\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    order: -1;\n  }\n`;\n\nconst ModernLevelTitle = styled(Heading)`\n  margin-bottom: ${theme.spacing[1]};\n  background: ${theme.colors.gradients.primary};\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n`;\n\nconst ModernLevelTheme = styled(Text)`\n  opacity: 0.8;\n  font-weight: ${theme.typography.fontWeight.medium};\n`;\n\nconst ModernConnectionStatus = styled(Badge)`\n  background: ${props => props.isConnected\n    ? 'rgba(34, 197, 94, 0.15)'\n    : 'rgba(239, 68, 68, 0.15)'\n  };\n  color: ${props => props.isConnected\n    ? theme.colors.success[700]\n    : theme.colors.error[700]\n  };\n  border: 1px solid ${props => props.isConnected\n    ? theme.colors.success[200]\n    : theme.colors.error[200]\n  };\n  backdrop-filter: blur(10px);\n  cursor: ${props => props.isConnected ? 'default' : 'pointer'};\n\n  &:hover {\n    background: ${props => props.isConnected\n      ? 'rgba(34, 197, 94, 0.2)'\n      : 'rgba(239, 68, 68, 0.2)'\n    };\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    width: 100%;\n    justify-content: center;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: 0.5rem 0.75rem;\n    font-size: 0.75rem;\n    \n    .connection-text {\n      display: none; /* Hide text on mobile, show only icon */\n    }\n  }\n`;\n\nconst MainContent = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 500px;\n  gap: ${theme.spacing[6]};\n  max-width: 1400px;\n  margin: 0 auto;\n  align-items: start;\n\n  @media (max-width: ${theme.breakpoints.xl}) {\n    grid-template-columns: 1fr 450px;\n    gap: ${theme.spacing[5]};\n    max-width: 1200px;\n  }\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    grid-template-columns: 1fr;\n    gap: ${theme.spacing[6]};\n    max-width: 800px;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    /* On mobile, show side by side for simultaneous viewing */\n    display: grid;\n    grid-template-columns: 1fr 1fr;\n    gap: ${theme.spacing[2]};\n    max-width: 100%;\n    height: 100vh;\n    padding: ${theme.spacing[2]};\n  }\n`;\n\nconst FlashCardSection = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  animation: ${fadeIn} 0.6s ease;\n  position: sticky;\n  top: ${theme.spacing[8]};\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    position: static;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    /* Mobile: optimize for side-by-side viewing */\n    height: 100%;\n    overflow-y: auto;\n    padding: ${theme.spacing[1]};\n    gap: ${theme.spacing[2]};\n  }\n`;\n\nconst ProgressSection = styled.div`\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20px;\n  padding: 1rem;\n  margin-bottom: 1.5rem;\n  text-align: center;\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  width: 100%;\n  max-width: 480px;\n  margin-left: auto;\n  margin-right: auto;\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    max-width: 420px;\n    padding: 0.875rem;\n    margin-bottom: 1.5rem;\n    border-radius: 18px;\n  }\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    max-width: 380px;\n    padding: 0.75rem;\n    margin-bottom: 1.5rem;\n    border-radius: 16px;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    max-width: 100%;\n    padding: ${theme.spacing[2]};\n    margin-bottom: ${theme.spacing[2]};\n    border-radius: 12px;\n  }\n`;\n\nconst ProgressTitle = styled.h3`\n  font-size: 1.125rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin-bottom: 0.75rem;\n`;\n\nconst ProgressBar = styled.div`\n  width: 100%;\n  height: 14px;\n  background: #e2e8f0;\n  border-radius: 8px;\n  overflow: hidden;\n  margin-bottom: 0.75rem;\n  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    height: 12px;\n    margin-bottom: 0.75rem;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    height: 10px;\n    margin-bottom: 0.75rem;\n  }\n`;\n\nconst ProgressFill = styled.div`\n  height: 100%;\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #10b981);\n  border-radius: 8px;\n  transition: width 0.5s ease;\n  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    border-radius: 7px;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    border-radius: 6px;\n  }\n`;\n\nconst ProgressText = styled.div`\n  font-size: 0.875rem;\n  color: #64748b;\n  font-weight: 600;\n`;\n\nconst Controls = styled.div`\n  display: flex;\n  gap: 1rem;\n  margin-top: 2rem;\n  justify-content: center;\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    gap: 0.75rem;\n    margin-top: 1.5rem;\n  }\n  \n  @media (max-width: 768px) {\n    gap: 0.75rem;\n    margin-top: 1.5rem;\n    flex-wrap: wrap;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    gap: ${theme.spacing[1]};\n    margin-top: ${theme.spacing[2]};\n    padding: 0;\n    flex-direction: row;\n    flex-wrap: wrap;\n  }\n`;\n\nconst ControlButton = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 1rem 2rem;\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-height: 48px; /* Minimum touch target size */\n  touch-action: manipulation; /* Optimize for touch */\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    padding: 1rem 2rem;\n    font-size: 1rem;\n    min-height: 48px;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: 0.5rem 1rem;\n    font-size: 0.75rem;\n    min-height: 36px;\n    flex: 1;\n    min-width: 80px;\n  }\n  \n  ${props => {\n    if (props.variant === 'primary') {\n      return `\n        background: linear-gradient(135deg, #3b82f6, #8b5cf6);\n        color: white;\n        &:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);\n        }\n      `;\n    }\n    if (props.variant === 'success') {\n      return `\n        background: linear-gradient(135deg, #10b981, #34d399);\n        color: white;\n        &:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);\n        }\n      `;\n    }\n    return `\n      background: rgba(255, 255, 255, 0.9);\n      color: #64748b;\n      &:hover {\n        background: white;\n        transform: translateY(-2px);\n      }\n    `;\n  }}\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none !important;\n  }\n  \n  @media (max-width: 768px) {\n    padding: 0.75rem 1.5rem;\n    font-size: 0.875rem;\n  }\n`;\n\nconst CameraSection = styled(Card)`\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n  border-radius: 20px;\n  padding: 1.5rem;\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    order: 1; /* Show after flash card on mobile */\n    padding: 1.25rem;\n    border-radius: 18px;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    /* Mobile: optimize for side-by-side viewing */\n    order: 0; /* Reset order for side-by-side layout */\n    margin: 0;\n    border-radius: 12px;\n    padding: ${theme.spacing[2]};\n    height: 100%;\n    overflow-y: auto;\n    display: flex;\n    flex-direction: column;\n  }\n`;\n\nconst CameraTitle = styled.h3`\n  font-size: 1.25rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin-bottom: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    font-size: 1.125rem;\n    margin-bottom: 0.75rem;\n    gap: 0.5rem;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    font-size: 0.875rem;\n    margin-bottom: ${theme.spacing[1]};\n    gap: 0.25rem;\n  }\n`;\n\nconst WebcamContainer = styled.div`\n  position: relative;\n  border-radius: 18px;\n  overflow: hidden;\n  background: #000;\n  margin-bottom: 1rem;\n  aspect-ratio: 4/3; /* Maintain aspect ratio */\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n  border: 2px solid rgba(255, 255, 255, 0.1);\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    border-radius: 16px;\n    margin-bottom: 0.75rem;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    border-radius: 8px;\n    margin-bottom: ${theme.spacing[2]};\n    aspect-ratio: 3/2; /* More compact ratio for mobile side-by-side */\n    flex-shrink: 0;\n  }\n`;\n\nconst WebcamError = styled.div`\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: #f8fafc;\n  color: #64748b;\n  text-align: center;\n  padding: 1rem;\n  \n  svg {\n    width: 48px;\n    height: 48px;\n    margin-bottom: 0.75rem;\n    color: #94a3b8;\n  }\n`;\n\nconst StatusOverlay = styled.div`\n  position: absolute;\n  top: 1rem;\n  left: 1rem;\n  right: 1rem;\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 0.75rem;\n  border-radius: 8px;\n  font-weight: 600;\n  text-align: center;\n  font-size: 0.875rem;\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    top: 0.5rem;\n    left: 0.5rem;\n    right: 0.5rem;\n    padding: 0.5rem;\n    font-size: 0.75rem;\n  }\n`;\n\nconst PredictionDisplay = styled.div`\n  background: linear-gradient(135deg, #f8fafc, #f1f5f9);\n  border-radius: 14px;\n  padding: 1.25rem;\n  text-align: center;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    padding: 1rem;\n    border-radius: 12px;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: ${theme.spacing[2]};\n    margin: 0;\n    border-radius: 8px;\n    flex-shrink: 0;\n  }\n`;\n\nconst PredictionText = styled.div`\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin-bottom: 0.75rem;\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    font-size: 1.125rem;\n    margin-bottom: 0.5rem;\n  }\n`;\n\nconst ConfidenceText = styled.div`\n  font-size: 0.875rem;\n  color: #64748b;\n`;\n\nconst CompletionModal = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  animation: ${fadeIn} 0.3s ease;\n`;\n\nconst ModalContent = styled.div`\n  background: white;\n  border-radius: 24px;\n  padding: 3rem;\n  text-align: center;\n  max-width: 500px;\n  margin: 1rem;\n  animation: ${celebration} 0.6s ease;\n`;\n\nconst ModalTitle = styled.h2`\n  font-size: 2.5rem;\n  font-weight: 800;\n  color: #1e293b;\n  margin-bottom: 1rem;\n`;\n\nconst ModalText = styled.p`\n  font-size: 1.125rem;\n  color: #64748b;\n  margin-bottom: 2rem;\n  line-height: 1.6;\n`;\n\nconst ModalButtons = styled.div`\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n`;\n\nconst FlashCardTraining = ({ \n  level, \n  onBack, \n  userProgress = {}, \n  onProgressUpdate \n}) => {\n  const webcamRef = useRef(null);\n  const [currentCardIndex, setCurrentCardIndex] = useState(0);\n  const [completedCards, setCompletedCards] = useState(new Set());\n  const [cardStates, setCardStates] = useState({});\n  const [slideDirection, setSlideDirection] = useState(null);\n  const [showCompletion, setShowCompletion] = useState(false);\n  const [isCapturing, setIsCapturing] = useState(false);\n  const [webcamError, setWebcamError] = useState(false);\n  const [webcamLoading, setWebcamLoading] = useState(true);\n  const [constraintAttempt, setConstraintAttempt] = useState(0);\n\n  const levelInfo = getLevelInfo(level);\n  const signs = getSignsForLevel(level);\n  const currentSign = signs[currentCardIndex];\n\n  // Use sign detection hook\n  const {\n    isConnected,\n    prediction,\n    isAIRecording,\n    recordingStatus,\n    signMatched,\n    targetSign,\n    currentKeypoints,\n    startRecording: startAIRecording,\n    stopRecording: stopAIRecording,\n    startFrameCapture,\n    retryConnection,\n    setLevel\n  } = useSignDetection();\n\n  const progress = (completedCards.size / signs.length) * 100;\n\n  // Webcam error handling\n  const handleWebcamError = useCallback((error) => {\n    console.error('Webcam error:', error);\n    \n    // Try different constraint configurations\n    if (constraintAttempt < 3) {\n      setConstraintAttempt(prev => prev + 1);\n      setWebcamLoading(true);\n      return;\n    }\n    \n    setWebcamError(true);\n    setWebcamLoading(false);\n  }, [constraintAttempt]);\n\n  const handleWebcamLoad = useCallback(() => {\n    setWebcamError(false);\n    setWebcamLoading(false);\n  }, []);\n\n  const retryWebcam = useCallback(() => {\n    setWebcamError(false);\n    setWebcamLoading(true);\n    setConstraintAttempt(0);\n  }, []);\n\n  // Chrome-compatible video constraints with fallback\n  const getVideoConstraints = useCallback(() => {\n    const constraints = [\n      // First attempt: Full constraints\n      {\n        width: { ideal: 640, min: 320 },\n        height: { ideal: 480, min: 240 },\n        facingMode: \"user\",\n        aspectRatio: { ideal: 4/3 }\n      },\n      // Second attempt: Simplified for Chrome\n      {\n        width: { ideal: 640 },\n        height: { ideal: 480 },\n        facingMode: \"user\"\n      },\n      // Third attempt: Minimal constraints\n      {\n        facingMode: \"user\"\n      },\n      // Fourth attempt: No constraints\n      {}\n    ];\n\n    return constraints[constraintAttempt] || constraints[constraints.length - 1];\n  }, [constraintAttempt]);\n\n  // Start detection when connected\n  const startDetection = useCallback(() => {\n    if (!webcamRef.current) return;\n    setIsCapturing(true);\n    startFrameCapture(webcamRef, 100);\n  }, [startFrameCapture]);\n\n  // Save training data when sign is detected correctly\n  const saveTrainingData = useCallback(async (signName, keypoints, confidence) => {\n    try {\n      console.log(`💾 Saving training data for ${signName} with confidence ${confidence}`);\n      \n      const response = await fetch('http://localhost:8000/save-training-data', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          sign_name: signName,\n          keypoints: keypoints,\n          confidence: confidence,\n          timestamp: new Date().toISOString()\n        })\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        console.log(`✅ Training data saved: ${result.message}`);\n        return true;\n      } else {\n        console.error('❌ Failed to save training data');\n        return false;\n      }\n    } catch (error) {\n      console.error('❌ Error saving training data:', error);\n      return false;\n    }\n  }, []);\n\n  const nextCard = useCallback(() => {\n    if (currentCardIndex < signs.length - 1) {\n      setSlideDirection('right');\n      setCurrentCardIndex(prev => prev + 1);\n      setCardStates(prev => ({ ...prev, [currentCardIndex]: null }));\n      setTimeout(() => setSlideDirection(null), 500);\n    }\n  }, [currentCardIndex, signs.length]);\n\n  // Handle sign detection success with automatic recording and training data saving\n  useEffect(() => {\n    console.log(`🔍 Debug: signMatched=${signMatched}, currentSign=${currentSign?.name}, prediction=${prediction?.sign}, confidence=${prediction?.confidence}`);\n    \n    if (signMatched && currentSign && prediction?.sign?.toLowerCase() === currentSign.name.toLowerCase()) {\n      console.log(`✅ Sign match confirmed: ${currentSign.name} with confidence ${prediction.confidence}`);\n      \n      // Only proceed if this card hasn't been completed yet\n      if (!completedCards.has(currentCardIndex)) {\n        console.log(`🎯 Correct sign detected: ${currentSign.name} with confidence ${prediction.confidence}`);\n        \n        // Save training data immediately when sign is detected correctly\n        const saveTrainingDataAsync = async () => {\n          if (currentKeypoints && prediction?.confidence >= 0.5) {\n            const saved = await saveTrainingData(currentSign.name, currentKeypoints, prediction.confidence);\n            if (saved) {\n              console.log(`✅ Training data saved successfully for ${currentSign.name}`);\n            }\n          }\n        };\n        \n        // Save training data\n        saveTrainingDataAsync();\n\n        // Start automatic recording for additional training data\n        if (!isAIRecording && isConnected) {\n          console.log(`🎬 Starting automatic recording for ${currentSign.name}...`);\n          startAIRecording(currentSign.name, true); // Start immediate recording session\n\n          // Stop recording after 3 seconds\n          setTimeout(() => {\n            stopAIRecording();\n            console.log(`✅ Automatic recording completed for: ${currentSign.name}`);\n          }, 3000);\n        }\n\n        // Mark card as completed\n        setCardStates(prev => ({ ...prev, [currentCardIndex]: 'correct' }));\n        setCompletedCards(prev => new Set([...prev, currentCardIndex]));\n\n        // Update progress\n        if (onProgressUpdate) {\n          const newCompletedCount = completedCards.size + 1;\n          onProgressUpdate(level, newCompletedCount, signs.length);\n        }\n\n        // Auto-advance after 2 seconds (allowing time for user to see success)\n        setTimeout(() => {\n          if (currentCardIndex < signs.length - 1) {\n            nextCard();\n          } else {\n            // Level completed\n            setShowCompletion(true);\n            if (onProgressUpdate) {\n              onProgressUpdate(level, signs.length, signs.length);\n            }\n          }\n        }, 2000);\n      } else {\n        console.log(`⚠️ Card ${currentCardIndex} already completed`);\n      }\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [signMatched, currentSign, prediction, currentCardIndex, signs.length, level, onProgressUpdate, isAIRecording, isConnected, startAIRecording, stopAIRecording, completedCards, saveTrainingData, currentKeypoints]);\n\n  // Set level when connected\n  useEffect(() => {\n    if (isConnected && level) {\n      setLevel(level);\n    }\n  }, [isConnected, level, setLevel]);\n\n  // Auto-start detection when connected\n  useEffect(() => {\n    if (isConnected && webcamRef.current && !isCapturing) {\n      startDetection();\n    }\n  }, [isConnected, startDetection, isCapturing]);\n\n  // Set target sign when current sign changes\n  useEffect(() => {\n    if (isConnected && currentSign) {\n      console.log(`🎯 Setting target sign to: ${currentSign.name}`);\n      startAIRecording(currentSign.name, false); // Set target sign without starting session\n    }\n  }, [isConnected, currentSign, startAIRecording]);\n\n  const prevCard = useCallback(() => {\n    if (currentCardIndex > 0) {\n      setSlideDirection('left');\n      setCurrentCardIndex(prev => prev - 1);\n      setCardStates(prev => ({ ...prev, [currentCardIndex]: null }));\n      setTimeout(() => setSlideDirection(null), 500);\n    }\n  }, [currentCardIndex]);\n\n  const retryCard = useCallback(() => {\n    setCardStates(prev => ({ ...prev, [currentCardIndex]: null }));\n    setCompletedCards(prev => {\n      const newSet = new Set(prev);\n      newSet.delete(currentCardIndex);\n      return newSet;\n    });\n  }, [currentCardIndex]);\n\n  const handleLevelComplete = () => {\n    setShowCompletion(false);\n    onBack();\n  };\n\n  const handleNextLevel = () => {\n    setShowCompletion(false);\n    // This would typically navigate to the next level\n    onBack();\n  };\n\n  if (!levelInfo || !currentSign) {\n    return (\n      <Container>\n        <div style={{ color: 'white', textAlign: 'center', padding: '2rem' }}>\n          <h2>Level not found</h2>\n          <button onClick={onBack}>Go Back</button>\n        </div>\n      </Container>\n    );\n  }\n\n  const isCurrentCardCompleted = completedCards.has(currentCardIndex);\n  const currentCardState = cardStates[currentCardIndex];\n\n  return (\n    <ModernContainer>\n      <ModernHeader size=\"md\">\n        <ModernBackButton variant=\"ghost\" size=\"md\" onClick={onBack}>\n          <ArrowLeft size={20} />\n          Back to Levels\n        </ModernBackButton>\n\n        <ModernLevelInfo>\n          <ModernLevelTitle level={3}>\n            Level {level}: {levelInfo.name}\n          </ModernLevelTitle>\n          <ModernLevelTheme size=\"lg\">\n            {levelInfo.theme}\n          </ModernLevelTheme>\n        </ModernLevelInfo>\n\n        <ModernConnectionStatus\n          isConnected={isConnected}\n          onClick={!isConnected ? retryConnection : undefined}\n        >\n          {isConnected ? <Wifi size={18} /> : <WifiOff size={18} />}\n          <span className=\"connection-text\">\n            {isConnected ? 'Connected' : 'Disconnected'}\n          </span>\n          {!isConnected && <RefreshCw size={14} />}\n        </ModernConnectionStatus>\n      </ModernHeader>\n\n      <MainContent>\n        <FlashCardSection>\n          <ProgressSection>\n            <ProgressTitle>Level Progress</ProgressTitle>\n            <ProgressBar>\n              <ProgressFill style={{ width: `${progress}%` }} />\n            </ProgressBar>\n            <ProgressText>\n              {completedCards.size} of {signs.length} signs completed ({Math.round(progress)}%)\n            </ProgressText>\n          </ProgressSection>\n\n          <FlashCard\n            sign={currentSign}\n            cardNumber={currentCardIndex + 1}\n            totalCards={signs.length}\n            isCorrect={currentCardState === 'correct'}\n            isIncorrect={currentCardState === 'incorrect'}\n            isDetecting={isConnected && !isCurrentCardCompleted}\n            slideDirection={slideDirection}\n            progress={(currentCardIndex / signs.length) * 100}\n          />\n\n          <Controls>\n            <ControlButton\n              onClick={prevCard}\n              disabled={currentCardIndex === 0}\n            >\n              <ArrowLeft size={20} />\n              Previous\n            </ControlButton>\n\n            {isCurrentCardCompleted ? (\n              <ControlButton\n                variant=\"success\"\n                onClick={nextCard}\n                disabled={currentCardIndex === signs.length - 1}\n              >\n                <CheckCircle size={20} />\n                Next Card\n              </ControlButton>\n            ) : (\n              <ControlButton\n                onClick={retryCard}\n                disabled={!isConnected}\n              >\n                <RotateCcw size={20} />\n                Retry\n              </ControlButton>\n            )}\n\n            <ControlButton\n              onClick={nextCard}\n              disabled={currentCardIndex === signs.length - 1}\n            >\n              Next\n              <ArrowRight size={20} />\n            </ControlButton>\n          </Controls>\n        </FlashCardSection>\n\n        <CameraSection>\n          <CameraTitle>\n            <Camera size={24} />\n            Camera Feed\n          </CameraTitle>\n\n          <WebcamContainer>\n            {webcamError ? (\n              <WebcamError>\n                <AlertTriangle />\n                <div style={{ fontSize: '1.1rem', fontWeight: '600', marginBottom: '8px' }}>\n                  Camera Access Error\n                </div>\n                <div style={{ fontSize: '0.9rem', marginBottom: '16px', lineHeight: '1.4' }}>\n                  Chrome requires camera permissions. Please:\n                </div>\n                <div style={{ fontSize: '0.85rem', textAlign: 'left', lineHeight: '1.5' }}>\n                  1. Click the camera icon in the address bar<br/>\n                  2. Select \"Allow\" for camera access<br/>\n                  3. Refresh the page\n                </div>\n                <button\n                  onClick={retryWebcam}\n                  style={{\n                    marginTop: '16px',\n                    padding: '8px 16px',\n                    background: '#3b82f6',\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '8px',\n                    cursor: 'pointer',\n                    fontSize: '0.9rem',\n                    fontWeight: '500'\n                  }}\n                >\n                  Retry Camera\n                </button>\n              </WebcamError>\n            ) : (\n              <>\n                <Webcam\n                  key={`webcam-${constraintAttempt}`}\n                  ref={webcamRef}\n                  audio={false}\n                  width=\"100%\"\n                  height=\"auto\"\n                  screenshotFormat=\"image/jpeg\"\n                  videoConstraints={getVideoConstraints()}\n                  onUserMediaError={handleWebcamError}\n                  onUserMedia={handleWebcamLoad}\n                  forceScreenshotSourceSize={true}\n                />\n\n                {webcamLoading && (\n                  <div style={{\n                    position: 'absolute',\n                    top: '50%',\n                    left: '50%',\n                    transform: 'translate(-50%, -50%)',\n                    background: 'rgba(0,0,0,0.7)',\n                    color: 'white',\n                    padding: '12px 20px',\n                    borderRadius: '8px',\n                    fontSize: '0.9rem',\n                    zIndex: 10\n                  }}>\n                    Loading camera...\n                  </div>\n                )}\n\n                {recordingStatus && (\n                  <StatusOverlay>\n                    {recordingStatus}\n                  </StatusOverlay>\n                )}\n              </>\n            )}\n          </WebcamContainer>\n\n          <PredictionDisplay>\n            <PredictionText>\n              {prediction?.sign ? `Detected: ${prediction.sign}` : 'Show the sign to get started'}\n            </PredictionText>\n            {prediction?.confidence && (\n              <ConfidenceText>\n                Confidence: {Math.round(prediction.confidence * 100)}%\n              </ConfidenceText>\n            )}\n          </PredictionDisplay>\n        </CameraSection>\n      </MainContent>\n\n      {showCompletion && (\n        <CompletionModal>\n          <ModalContent>\n            <Trophy size={80} style={{ color: '#f59e0b', marginBottom: '1rem' }} />\n            <ModalTitle>🎉 Level Complete!</ModalTitle>\n            <ModalText>\n              Congratulations! You've successfully completed Level {level}: {levelInfo.name}.\n              You've mastered all {signs.length} signs in this level!\n            </ModalText>\n            <ModalButtons>\n              <ControlButton onClick={handleLevelComplete}>\n                <Home size={20} />\n                Back to Levels\n              </ControlButton>\n              {level < 5 && (\n                <ControlButton variant=\"primary\" onClick={handleNextLevel}>\n                  <Target size={20} />\n                  Next Level\n                </ControlButton>\n              )}\n            </ModalButtons>\n          </ModalContent>\n        </CompletionModal>\n      )}\n    </ModernContainer>\n  );\n};\n\nexport default FlashCardTraining;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AACvE,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;AACrD,OAAOC,MAAM,MAAM,cAAc;AACjC,SACEC,SAAS,EACTC,UAAU,EACVC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,IAAI,EACJC,OAAO,EACPC,SAAS,EACTC,WAAW,EACXC,MAAM,EACNC,MAAM,EACNC,GAAG,EACHC,QAAQ,EACRC,KAAK,EACLC,aAAa,QACR,cAAc;AACrB,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,oBAAoB;AACnE,SAASC,KAAK,QAAQ,iBAAiB;AACvC,SAASC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAEC,KAAK,QAAQ,uBAAuB;;AAErF;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,MAAM,GAAGhC,SAAS;AACxB;AACA;AACA,CAAC;AAED,MAAMiC,WAAW,GAAGjC,SAAS;AAC7B;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMkC,eAAe,GAAGnC,MAAM,CAACoC,GAAG;AAClC;AACA;AACA,aAAad,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBf,KAAK,CAACgB,WAAW,CAACC,EAAE;AAC3C,eAAejB,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AAC/B;AACA;AACA,uBAAuBf,KAAK,CAACgB,WAAW,CAACE,EAAE;AAC3C,eAAelB,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AAC/B;AACA;AACA,CAAC;AAACI,EAAA,GAlCIN,eAAe;AAmCrB,MAAMO,MAAM,GAAG1C,MAAM,CAACoC,GAAG;AACzB;AACA;AACA;AACA,mBAAmBd,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AACnC;AACA,uBAAuBf,KAAK,CAACgB,WAAW,CAACC,EAAE;AAC3C;AACA,WAAWjB,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AAC3B;AACA,CAAC;AAED,MAAMM,YAAY,GAAG3C,MAAM,CAACwB,IAAI,CAAC;AACjC;AACA;AACA;AACA,mBAAmBF,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AACnC;AACA;AACA;AACA,gBAAgBf,KAAK,CAACsB,OAAO,CAACC,EAAE;AAChC;AACA,uBAAuBvB,KAAK,CAACgB,WAAW,CAACC,EAAE;AAC3C;AACA,WAAWjB,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AAC3B;AACA,qBAAqBf,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AACrC;AACA;AACA,uBAAuBf,KAAK,CAACgB,WAAW,CAACE,EAAE;AAC3C,qBAAqBlB,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AACrC,eAAef,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AAC/B;AACA;AACA,uBAAuBf,KAAK,CAACgB,WAAW,CAACE,EAAE;AAC3C,qBAAqBlB,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AACrC;AACA,CAAC;AAACS,GAAA,GAzBIH,YAAY;AA2BlB,MAAMI,gBAAgB,GAAG/C,MAAM,CAACyB,MAAM,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBH,KAAK,CAACgB,WAAW,CAACC,EAAE;AAC3C;AACA;AACA;AACA,uBAAuBjB,KAAK,CAACgB,WAAW,CAACE,EAAE;AAC3C;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GA7BID,gBAAgB;AA+BtB,MAAME,eAAe,GAAGjD,MAAM,CAACoC,GAAG;AAClC;AACA,WAAWd,KAAK,CAAC4B,MAAM,CAACC,IAAI,CAACC,OAAO;AACpC;AACA,uBAAuB9B,KAAK,CAACgB,WAAW,CAACC,EAAE;AAC3C;AACA;AACA,CAAC;AAACc,GAAA,GAPIJ,eAAe;AASrB,MAAMK,gBAAgB,GAAGtD,MAAM,CAAC0B,OAAO,CAAC;AACxC,mBAAmBJ,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AACnC,gBAAgBf,KAAK,CAAC4B,MAAM,CAACK,SAAS,CAACH,OAAO;AAC9C;AACA;AACA;AACA,CAAC;AAACI,GAAA,GANIF,gBAAgB;AAQtB,MAAMG,gBAAgB,GAAGzD,MAAM,CAAC2B,IAAI,CAAC;AACrC;AACA,iBAAiBL,KAAK,CAACoC,UAAU,CAACC,UAAU,CAACC,MAAM;AACnD,CAAC;AAACC,GAAA,GAHIJ,gBAAgB;AAKtB,MAAMK,sBAAsB,GAAG9D,MAAM,CAAC4B,KAAK,CAAC;AAC5C,gBAAgBmC,KAAK,IAAIA,KAAK,CAACC,WAAW,GACpC,yBAAyB,GACzB,yBAAyB;AAC/B,WACWD,KAAK,IAAIA,KAAK,CAACC,WAAW,GAC/B1C,KAAK,CAAC4B,MAAM,CAACe,OAAO,CAAC,GAAG,CAAC,GACzB3C,KAAK,CAAC4B,MAAM,CAACgB,KAAK,CAAC,GAAG,CAAC;AAC7B,sBACsBH,KAAK,IAAIA,KAAK,CAACC,WAAW,GAC1C1C,KAAK,CAAC4B,MAAM,CAACe,OAAO,CAAC,GAAG,CAAC,GACzB3C,KAAK,CAAC4B,MAAM,CAACgB,KAAK,CAAC,GAAG,CAAC;AAC7B;AACA,YACYH,KAAK,IAAIA,KAAK,CAACC,WAAW,GAAG,SAAS,GAAG,SAAS;AAC9D;AACA;AACA,kBAAkBD,KAAK,IAAIA,KAAK,CAACC,WAAW,GACpC,wBAAwB,GACxB,wBAAwB;AAChC;AACA;AACA,uBACuB1C,KAAK,CAACgB,WAAW,CAACC,EAAE;AAC3C;AACA;AACA;AACA;AACA,uBAAuBjB,KAAK,CAACgB,WAAW,CAACE,EAAE;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC2B,GAAA,GApCIL,sBAAsB;AAsC5B,MAAMM,WAAW,GAAGpE,MAAM,CAACoC,GAAG;AAC9B;AACA;AACA,SAASd,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AACzB;AACA;AACA;AACA;AACA,uBAAuBf,KAAK,CAACgB,WAAW,CAACO,EAAE;AAC3C;AACA,WAAWvB,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AAC3B;AACA;AACA;AACA,uBAAuBf,KAAK,CAACgB,WAAW,CAAC+B,EAAE;AAC3C;AACA,WAAW/C,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AAC3B;AACA;AACA;AACA,uBAAuBf,KAAK,CAACgB,WAAW,CAACE,EAAE;AAC3C;AACA;AACA;AACA,WAAWlB,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AAC3B;AACA;AACA,eAAef,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AAC/B;AACA,CAAC;AAACiC,GAAA,GA7BIF,WAAW;AA+BjB,MAAMG,gBAAgB,GAAGvE,MAAM,CAACoC,GAAG;AACnC;AACA;AACA;AACA,eAAeH,MAAM;AACrB;AACA,SAASX,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AACzB;AACA,uBAAuBf,KAAK,CAACgB,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA;AACA,uBAAuB/C,KAAK,CAACgB,WAAW,CAACE,EAAE;AAC3C;AACA;AACA;AACA,eAAelB,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AAC/B,WAAWf,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AAC3B;AACA,CAAC;AAACmC,GAAA,GAnBID,gBAAgB;AAqBtB,MAAME,eAAe,GAAGzE,MAAM,CAACoC,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBd,KAAK,CAACgB,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB/C,KAAK,CAACgB,WAAW,CAACC,EAAE;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBjB,KAAK,CAACgB,WAAW,CAACE,EAAE;AAC3C;AACA,eAAelB,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AAC/B,qBAAqBf,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AACrC;AACA;AACA,CAAC;AAACqC,GAAA,GAlCID,eAAe;AAoCrB,MAAME,aAAa,GAAG3E,MAAM,CAAC4E,EAAE;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,aAAa;AAOnB,MAAMG,WAAW,GAAG9E,MAAM,CAACoC,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBd,KAAK,CAACgB,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA;AACA;AACA,uBAAuB/C,KAAK,CAACgB,WAAW,CAACE,EAAE;AAC3C;AACA;AACA;AACA,CAAC;AAACuC,IAAA,GAlBID,WAAW;AAoBjB,MAAME,YAAY,GAAGhF,MAAM,CAACoC,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBd,KAAK,CAACgB,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA;AACA,uBAAuB/C,KAAK,CAACgB,WAAW,CAACE,EAAE;AAC3C;AACA;AACA,CAAC;AAACyC,IAAA,GAdID,YAAY;AAgBlB,MAAME,YAAY,GAAGlF,MAAM,CAACoC,GAAG;AAC/B;AACA;AACA;AACA,CAAC;AAAC+C,IAAA,GAJID,YAAY;AAMlB,MAAME,QAAQ,GAAGpF,MAAM,CAACoC,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA,uBAAuBd,KAAK,CAACgB,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB/C,KAAK,CAACgB,WAAW,CAACE,EAAE;AAC3C,WAAWlB,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AAC3B,kBAAkBf,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AAClC;AACA;AACA;AACA;AACA,CAAC;AAACgD,IAAA,GAxBID,QAAQ;AA0Bd,MAAME,aAAa,GAAGtF,MAAM,CAACuF,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBjE,KAAK,CAACgB,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA;AACA;AACA;AACA,uBAAuB/C,KAAK,CAACgB,WAAW,CAACE,EAAE;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIuB,KAAK,IAAI;EACT,IAAIA,KAAK,CAACyB,OAAO,KAAK,SAAS,EAAE;IAC/B,OAAO;AACb;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;EACH;EACA,IAAIzB,KAAK,CAACyB,OAAO,KAAK,SAAS,EAAE;IAC/B,OAAO;AACb;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;EACH;EACA,OAAO;AACX;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACH,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GApEIH,aAAa;AAsEnB,MAAMI,aAAa,GAAG1F,MAAM,CAACwB,IAAI,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBF,KAAK,CAACgB,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA;AACA;AACA;AACA,uBAAuB/C,KAAK,CAACgB,WAAW,CAACE,EAAE;AAC3C;AACA;AACA;AACA;AACA,eAAelB,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsD,IAAA,GAzBID,aAAa;AA2BnB,MAAME,WAAW,GAAG5F,MAAM,CAAC4E,EAAE;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBtD,KAAK,CAACgB,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA;AACA;AACA;AACA,uBAAuB/C,KAAK,CAACgB,WAAW,CAACE,EAAE;AAC3C;AACA,qBAAqBlB,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AACrC;AACA;AACA,CAAC;AAACwD,IAAA,GApBID,WAAW;AAsBjB,MAAME,eAAe,GAAG9F,MAAM,CAACoC,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBd,KAAK,CAACgB,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA;AACA;AACA,uBAAuB/C,KAAK,CAACgB,WAAW,CAACE,EAAE;AAC3C;AACA,qBAAqBlB,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AACrC;AACA;AACA;AACA,CAAC;AAAC0D,IAAA,GArBID,eAAe;AAuBrB,MAAME,WAAW,GAAGhG,MAAM,CAACoC,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC6D,IAAA,GAlBID,WAAW;AAoBjB,MAAME,aAAa,GAAGlG,MAAM,CAACoC,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBd,KAAK,CAACgB,WAAW,CAACE,EAAE;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC2D,IAAA,GApBID,aAAa;AAsBnB,MAAME,iBAAiB,GAAGpG,MAAM,CAACoC,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBd,KAAK,CAACgB,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA;AACA;AACA,uBAAuB/C,KAAK,CAACgB,WAAW,CAACE,EAAE;AAC3C,eAAelB,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACgE,IAAA,GAnBID,iBAAiB;AAqBvB,MAAME,cAAc,GAAGtG,MAAM,CAACoC,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA,uBAAuBd,KAAK,CAACgB,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA;AACA,CAAC;AAACkC,IAAA,GAVID,cAAc;AAYpB,MAAME,cAAc,GAAGxG,MAAM,CAACoC,GAAG;AACjC;AACA;AACA,CAAC;AAACqE,IAAA,GAHID,cAAc;AAKpB,MAAME,eAAe,GAAG1G,MAAM,CAACoC,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeH,MAAM;AACrB,CAAC;AAAC0E,IAAA,GAZID,eAAe;AAcrB,MAAME,YAAY,GAAG5G,MAAM,CAACoC,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,eAAeF,WAAW;AAC1B,CAAC;AAAC2E,IAAA,GARID,YAAY;AAUlB,MAAME,UAAU,GAAG9G,MAAM,CAAC+G,EAAE;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GALIF,UAAU;AAOhB,MAAMG,SAAS,GAAGjH,MAAM,CAACkH,CAAC;AAC1B;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GALIF,SAAS;AAOf,MAAMG,YAAY,GAAGpH,MAAM,CAACoC,GAAG;AAC/B;AACA;AACA;AACA,CAAC;AAACiF,IAAA,GAJID,YAAY;AAMlB,MAAME,iBAAiB,GAAGA,CAAC;EACzBC,KAAK;EACLC,MAAM;EACNC,YAAY,GAAG,CAAC,CAAC;EACjBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,SAAS,GAAG/H,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM,CAACgI,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlI,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACmI,cAAc,EAAEC,iBAAiB,CAAC,GAAGpI,QAAQ,CAAC,IAAIqI,GAAG,CAAC,CAAC,CAAC;EAC/D,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvI,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACwI,cAAc,EAAEC,iBAAiB,CAAC,GAAGzI,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC0I,cAAc,EAAEC,iBAAiB,CAAC,GAAG3I,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC4I,WAAW,EAAEC,cAAc,CAAC,GAAG7I,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8I,WAAW,EAAEC,cAAc,CAAC,GAAG/I,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgJ,aAAa,EAAEC,gBAAgB,CAAC,GAAGjJ,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACkJ,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnJ,QAAQ,CAAC,CAAC,CAAC;EAE7D,MAAMoJ,SAAS,GAAG3H,YAAY,CAACkG,KAAK,CAAC;EACrC,MAAM0B,KAAK,GAAG7H,gBAAgB,CAACmG,KAAK,CAAC;EACrC,MAAM2B,WAAW,GAAGD,KAAK,CAACpB,gBAAgB,CAAC;;EAE3C;EACA,MAAM;IACJ7D,WAAW;IACXmF,UAAU;IACVC,aAAa;IACbC,eAAe;IACfC,WAAW;IACXC,UAAU;IACVC,gBAAgB;IAChBC,cAAc,EAAEC,gBAAgB;IAChCC,aAAa,EAAEC,eAAe;IAC9BC,iBAAiB;IACjBC,eAAe;IACfC;EACF,CAAC,GAAG5I,gBAAgB,CAAC,CAAC;EAEtB,MAAM6I,QAAQ,GAAIjC,cAAc,CAACkC,IAAI,GAAGhB,KAAK,CAACiB,MAAM,GAAI,GAAG;;EAE3D;EACA,MAAMC,iBAAiB,GAAGrK,WAAW,CAAEoE,KAAK,IAAK;IAC/CkG,OAAO,CAAClG,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;;IAErC;IACA,IAAI4E,iBAAiB,GAAG,CAAC,EAAE;MACzBC,oBAAoB,CAACsB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACtCxB,gBAAgB,CAAC,IAAI,CAAC;MACtB;IACF;IAEAF,cAAc,CAAC,IAAI,CAAC;IACpBE,gBAAgB,CAAC,KAAK,CAAC;EACzB,CAAC,EAAE,CAACC,iBAAiB,CAAC,CAAC;EAEvB,MAAMwB,gBAAgB,GAAGxK,WAAW,CAAC,MAAM;IACzC6I,cAAc,CAAC,KAAK,CAAC;IACrBE,gBAAgB,CAAC,KAAK,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM0B,WAAW,GAAGzK,WAAW,CAAC,MAAM;IACpC6I,cAAc,CAAC,KAAK,CAAC;IACrBE,gBAAgB,CAAC,IAAI,CAAC;IACtBE,oBAAoB,CAAC,CAAC,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyB,mBAAmB,GAAG1K,WAAW,CAAC,MAAM;IAC5C,MAAM2K,WAAW,GAAG;IAClB;IACA;MACEC,KAAK,EAAE;QAAEC,KAAK,EAAE,GAAG;QAAEC,GAAG,EAAE;MAAI,CAAC;MAC/BC,MAAM,EAAE;QAAEF,KAAK,EAAE,GAAG;QAAEC,GAAG,EAAE;MAAI,CAAC;MAChCE,UAAU,EAAE,MAAM;MAClBC,WAAW,EAAE;QAAEJ,KAAK,EAAE,CAAC,GAAC;MAAE;IAC5B,CAAC;IACD;IACA;MACED,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAC;MACrBE,MAAM,EAAE;QAAEF,KAAK,EAAE;MAAI,CAAC;MACtBG,UAAU,EAAE;IACd,CAAC;IACD;IACA;MACEA,UAAU,EAAE;IACd,CAAC;IACD;IACA,CAAC,CAAC,CACH;IAED,OAAOL,WAAW,CAAC3B,iBAAiB,CAAC,IAAI2B,WAAW,CAACA,WAAW,CAACP,MAAM,GAAG,CAAC,CAAC;EAC9E,CAAC,EAAE,CAACpB,iBAAiB,CAAC,CAAC;;EAEvB;EACA,MAAMkC,cAAc,GAAGlL,WAAW,CAAC,MAAM;IACvC,IAAI,CAAC8H,SAAS,CAACqD,OAAO,EAAE;IACxBxC,cAAc,CAAC,IAAI,CAAC;IACpBoB,iBAAiB,CAACjC,SAAS,EAAE,GAAG,CAAC;EACnC,CAAC,EAAE,CAACiC,iBAAiB,CAAC,CAAC;;EAEvB;EACA,MAAMqB,gBAAgB,GAAGpL,WAAW,CAAC,OAAOqL,QAAQ,EAAEC,SAAS,EAAEC,UAAU,KAAK;IAC9E,IAAI;MACFjB,OAAO,CAACkB,GAAG,CAAC,+BAA+BH,QAAQ,oBAAoBE,UAAU,EAAE,CAAC;MAEpF,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,0CAA0C,EAAE;QACvEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,SAAS,EAAEX,QAAQ;UACnBC,SAAS,EAAEA,SAAS;UACpBC,UAAU,EAAEA,UAAU;UACtBU,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACpC,CAAC;MACH,CAAC,CAAC;MAEF,IAAIV,QAAQ,CAACW,EAAE,EAAE;QACf,MAAMC,MAAM,GAAG,MAAMZ,QAAQ,CAACa,IAAI,CAAC,CAAC;QACpChC,OAAO,CAACkB,GAAG,CAAC,0BAA0Ba,MAAM,CAACE,OAAO,EAAE,CAAC;QACvD,OAAO,IAAI;MACb,CAAC,MAAM;QACLjC,OAAO,CAAClG,KAAK,CAAC,gCAAgC,CAAC;QAC/C,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdkG,OAAO,CAAClG,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO,KAAK;IACd;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMoI,QAAQ,GAAGxM,WAAW,CAAC,MAAM;IACjC,IAAI+H,gBAAgB,GAAGoB,KAAK,CAACiB,MAAM,GAAG,CAAC,EAAE;MACvC7B,iBAAiB,CAAC,OAAO,CAAC;MAC1BP,mBAAmB,CAACuC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACrClC,aAAa,CAACkC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACxC,gBAAgB,GAAG;MAAK,CAAC,CAAC,CAAC;MAC9D0E,UAAU,CAAC,MAAMlE,iBAAiB,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;IAChD;EACF,CAAC,EAAE,CAACR,gBAAgB,EAAEoB,KAAK,CAACiB,MAAM,CAAC,CAAC;;EAEpC;EACAnK,SAAS,CAAC,MAAM;IAAA,IAAAyM,gBAAA;IACdpC,OAAO,CAACkB,GAAG,CAAC,yBAAyBhC,WAAW,iBAAiBJ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEuD,IAAI,gBAAgBtD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEuD,IAAI,gBAAgBvD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkC,UAAU,EAAE,CAAC;IAE3J,IAAI/B,WAAW,IAAIJ,WAAW,IAAI,CAAAC,UAAU,aAAVA,UAAU,wBAAAqD,gBAAA,GAAVrD,UAAU,CAAEuD,IAAI,cAAAF,gBAAA,uBAAhBA,gBAAA,CAAkBG,WAAW,CAAC,CAAC,MAAKzD,WAAW,CAACuD,IAAI,CAACE,WAAW,CAAC,CAAC,EAAE;MACpGvC,OAAO,CAACkB,GAAG,CAAC,2BAA2BpC,WAAW,CAACuD,IAAI,oBAAoBtD,UAAU,CAACkC,UAAU,EAAE,CAAC;;MAEnG;MACA,IAAI,CAACtD,cAAc,CAAC6E,GAAG,CAAC/E,gBAAgB,CAAC,EAAE;QACzCuC,OAAO,CAACkB,GAAG,CAAC,6BAA6BpC,WAAW,CAACuD,IAAI,oBAAoBtD,UAAU,CAACkC,UAAU,EAAE,CAAC;;QAErG;QACA,MAAMwB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;UACxC,IAAIrD,gBAAgB,IAAI,CAAAL,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkC,UAAU,KAAI,GAAG,EAAE;YACrD,MAAMyB,KAAK,GAAG,MAAM5B,gBAAgB,CAAChC,WAAW,CAACuD,IAAI,EAAEjD,gBAAgB,EAAEL,UAAU,CAACkC,UAAU,CAAC;YAC/F,IAAIyB,KAAK,EAAE;cACT1C,OAAO,CAACkB,GAAG,CAAC,0CAA0CpC,WAAW,CAACuD,IAAI,EAAE,CAAC;YAC3E;UACF;QACF,CAAC;;QAED;QACAI,qBAAqB,CAAC,CAAC;;QAEvB;QACA,IAAI,CAACzD,aAAa,IAAIpF,WAAW,EAAE;UACjCoG,OAAO,CAACkB,GAAG,CAAC,uCAAuCpC,WAAW,CAACuD,IAAI,KAAK,CAAC;UACzE/C,gBAAgB,CAACR,WAAW,CAACuD,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;;UAE1C;UACAF,UAAU,CAAC,MAAM;YACf3C,eAAe,CAAC,CAAC;YACjBQ,OAAO,CAACkB,GAAG,CAAC,wCAAwCpC,WAAW,CAACuD,IAAI,EAAE,CAAC;UACzE,CAAC,EAAE,IAAI,CAAC;QACV;;QAEA;QACAtE,aAAa,CAACkC,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACxC,gBAAgB,GAAG;QAAU,CAAC,CAAC,CAAC;QACnEG,iBAAiB,CAACqC,IAAI,IAAI,IAAIpC,GAAG,CAAC,CAAC,GAAGoC,IAAI,EAAExC,gBAAgB,CAAC,CAAC,CAAC;;QAE/D;QACA,IAAIH,gBAAgB,EAAE;UACpB,MAAMqF,iBAAiB,GAAGhF,cAAc,CAACkC,IAAI,GAAG,CAAC;UACjDvC,gBAAgB,CAACH,KAAK,EAAEwF,iBAAiB,EAAE9D,KAAK,CAACiB,MAAM,CAAC;QAC1D;;QAEA;QACAqC,UAAU,CAAC,MAAM;UACf,IAAI1E,gBAAgB,GAAGoB,KAAK,CAACiB,MAAM,GAAG,CAAC,EAAE;YACvCoC,QAAQ,CAAC,CAAC;UACZ,CAAC,MAAM;YACL;YACA/D,iBAAiB,CAAC,IAAI,CAAC;YACvB,IAAIb,gBAAgB,EAAE;cACpBA,gBAAgB,CAACH,KAAK,EAAE0B,KAAK,CAACiB,MAAM,EAAEjB,KAAK,CAACiB,MAAM,CAAC;YACrD;UACF;QACF,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLE,OAAO,CAACkB,GAAG,CAAC,WAAWzD,gBAAgB,oBAAoB,CAAC;MAC9D;IACF;IACF;EACA,CAAC,EAAE,CAACyB,WAAW,EAAEJ,WAAW,EAAEC,UAAU,EAAEtB,gBAAgB,EAAEoB,KAAK,CAACiB,MAAM,EAAE3C,KAAK,EAAEG,gBAAgB,EAAE0B,aAAa,EAAEpF,WAAW,EAAE0F,gBAAgB,EAAEE,eAAe,EAAE7B,cAAc,EAAEmD,gBAAgB,EAAE1B,gBAAgB,CAAC,CAAC;;EAEtN;EACAzJ,SAAS,CAAC,MAAM;IACd,IAAIiE,WAAW,IAAIuD,KAAK,EAAE;MACxBwC,QAAQ,CAACxC,KAAK,CAAC;IACjB;EACF,CAAC,EAAE,CAACvD,WAAW,EAAEuD,KAAK,EAAEwC,QAAQ,CAAC,CAAC;;EAElC;EACAhK,SAAS,CAAC,MAAM;IACd,IAAIiE,WAAW,IAAI4D,SAAS,CAACqD,OAAO,IAAI,CAACzC,WAAW,EAAE;MACpDwC,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAChH,WAAW,EAAEgH,cAAc,EAAExC,WAAW,CAAC,CAAC;;EAE9C;EACAzI,SAAS,CAAC,MAAM;IACd,IAAIiE,WAAW,IAAIkF,WAAW,EAAE;MAC9BkB,OAAO,CAACkB,GAAG,CAAC,8BAA8BpC,WAAW,CAACuD,IAAI,EAAE,CAAC;MAC7D/C,gBAAgB,CAACR,WAAW,CAACuD,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;IAC7C;EACF,CAAC,EAAE,CAACzI,WAAW,EAAEkF,WAAW,EAAEQ,gBAAgB,CAAC,CAAC;EAEhD,MAAMsD,QAAQ,GAAGlN,WAAW,CAAC,MAAM;IACjC,IAAI+H,gBAAgB,GAAG,CAAC,EAAE;MACxBQ,iBAAiB,CAAC,MAAM,CAAC;MACzBP,mBAAmB,CAACuC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACrClC,aAAa,CAACkC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACxC,gBAAgB,GAAG;MAAK,CAAC,CAAC,CAAC;MAC9D0E,UAAU,CAAC,MAAMlE,iBAAiB,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;IAChD;EACF,CAAC,EAAE,CAACR,gBAAgB,CAAC,CAAC;EAEtB,MAAMoF,SAAS,GAAGnN,WAAW,CAAC,MAAM;IAClCqI,aAAa,CAACkC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACxC,gBAAgB,GAAG;IAAK,CAAC,CAAC,CAAC;IAC9DG,iBAAiB,CAACqC,IAAI,IAAI;MACxB,MAAM6C,MAAM,GAAG,IAAIjF,GAAG,CAACoC,IAAI,CAAC;MAC5B6C,MAAM,CAACC,MAAM,CAACtF,gBAAgB,CAAC;MAC/B,OAAOqF,MAAM;IACf,CAAC,CAAC;EACJ,CAAC,EAAE,CAACrF,gBAAgB,CAAC,CAAC;EAEtB,MAAMuF,mBAAmB,GAAGA,CAAA,KAAM;IAChC7E,iBAAiB,CAAC,KAAK,CAAC;IACxBf,MAAM,CAAC,CAAC;EACV,CAAC;EAED,MAAM6F,eAAe,GAAGA,CAAA,KAAM;IAC5B9E,iBAAiB,CAAC,KAAK,CAAC;IACxB;IACAf,MAAM,CAAC,CAAC;EACV,CAAC;EAED,IAAI,CAACwB,SAAS,IAAI,CAACE,WAAW,EAAE;IAC9B,oBACEpH,OAAA,CAACP,SAAS;MAAA+L,QAAA,eACRxL,OAAA;QAAKyL,KAAK,EAAE;UAAEC,KAAK,EAAE,OAAO;UAAEC,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBACnExL,OAAA;UAAAwL,QAAA,EAAI;QAAe;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBhM,OAAA;UAAQiM,OAAO,EAAEvG,MAAO;UAAA8F,QAAA,EAAC;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEhB;EAEA,MAAME,sBAAsB,GAAGjG,cAAc,CAAC6E,GAAG,CAAC/E,gBAAgB,CAAC;EACnE,MAAMoG,gBAAgB,GAAG/F,UAAU,CAACL,gBAAgB,CAAC;EAErD,oBACE/F,OAAA,CAACK,eAAe;IAAAmL,QAAA,gBACdxL,OAAA,CAACa,YAAY;MAACsH,IAAI,EAAC,IAAI;MAAAqD,QAAA,gBACrBxL,OAAA,CAACiB,gBAAgB;QAACyC,OAAO,EAAC,OAAO;QAACyE,IAAI,EAAC,IAAI;QAAC8D,OAAO,EAAEvG,MAAO;QAAA8F,QAAA,gBAC1DxL,OAAA,CAAC3B,SAAS;UAAC8J,IAAI,EAAE;QAAG;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kBAEzB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAkB,CAAC,eAEnBhM,OAAA,CAACmB,eAAe;QAAAqK,QAAA,gBACdxL,OAAA,CAACwB,gBAAgB;UAACiE,KAAK,EAAE,CAAE;UAAA+F,QAAA,GAAC,QACpB,EAAC/F,KAAK,EAAC,IAAE,EAACyB,SAAS,CAACyD,IAAI;QAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACnBhM,OAAA,CAAC2B,gBAAgB;UAACwG,IAAI,EAAC,IAAI;UAAAqD,QAAA,EACxBtE,SAAS,CAAC1H;QAAK;UAAAqM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAElBhM,OAAA,CAACgC,sBAAsB;QACrBE,WAAW,EAAEA,WAAY;QACzB+J,OAAO,EAAE,CAAC/J,WAAW,GAAG8F,eAAe,GAAGoE,SAAU;QAAAZ,QAAA,GAEnDtJ,WAAW,gBAAGlC,OAAA,CAACtB,IAAI;UAACyJ,IAAI,EAAE;QAAG;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGhM,OAAA,CAACrB,OAAO;UAACwJ,IAAI,EAAE;QAAG;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzDhM,OAAA;UAAMqM,SAAS,EAAC,iBAAiB;UAAAb,QAAA,EAC9BtJ,WAAW,GAAG,WAAW,GAAG;QAAc;UAAA2J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,EACN,CAAC9J,WAAW,iBAAIlC,OAAA,CAACpB,SAAS;UAACuJ,IAAI,EAAE;QAAG;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC,eAEfhM,OAAA,CAACsC,WAAW;MAAAkJ,QAAA,gBACVxL,OAAA,CAACyC,gBAAgB;QAAA+I,QAAA,gBACfxL,OAAA,CAAC2C,eAAe;UAAA6I,QAAA,gBACdxL,OAAA,CAAC6C,aAAa;YAAA2I,QAAA,EAAC;UAAc;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,eAC7ChM,OAAA,CAACgD,WAAW;YAAAwI,QAAA,eACVxL,OAAA,CAACkD,YAAY;cAACuI,KAAK,EAAE;gBAAE7C,KAAK,EAAE,GAAGV,QAAQ;cAAI;YAAE;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACdhM,OAAA,CAACoD,YAAY;YAAAoI,QAAA,GACVvF,cAAc,CAACkC,IAAI,EAAC,MAAI,EAAChB,KAAK,CAACiB,MAAM,EAAC,oBAAkB,EAACkE,IAAI,CAACC,KAAK,CAACrE,QAAQ,CAAC,EAAC,IACjF;UAAA;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAElBhM,OAAA,CAACZ,SAAS;UACRwL,IAAI,EAAExD,WAAY;UAClBoF,UAAU,EAAEzG,gBAAgB,GAAG,CAAE;UACjC0G,UAAU,EAAEtF,KAAK,CAACiB,MAAO;UACzBsE,SAAS,EAAEP,gBAAgB,KAAK,SAAU;UAC1CQ,WAAW,EAAER,gBAAgB,KAAK,WAAY;UAC9CS,WAAW,EAAE1K,WAAW,IAAI,CAACgK,sBAAuB;UACpD5F,cAAc,EAAEA,cAAe;UAC/B4B,QAAQ,EAAGnC,gBAAgB,GAAGoB,KAAK,CAACiB,MAAM,GAAI;QAAI;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eAEFhM,OAAA,CAACsD,QAAQ;UAAAkI,QAAA,gBACPxL,OAAA,CAACwD,aAAa;YACZyI,OAAO,EAAEf,QAAS;YAClB2B,QAAQ,EAAE9G,gBAAgB,KAAK,CAAE;YAAAyF,QAAA,gBAEjCxL,OAAA,CAAC3B,SAAS;cAAC8J,IAAI,EAAE;YAAG;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAEzB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,EAEfE,sBAAsB,gBACrBlM,OAAA,CAACwD,aAAa;YACZE,OAAO,EAAC,SAAS;YACjBuI,OAAO,EAAEzB,QAAS;YAClBqC,QAAQ,EAAE9G,gBAAgB,KAAKoB,KAAK,CAACiB,MAAM,GAAG,CAAE;YAAAoD,QAAA,gBAEhDxL,OAAA,CAACnB,WAAW;cAACsJ,IAAI,EAAE;YAAG;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAE3B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,gBAEhBhM,OAAA,CAACwD,aAAa;YACZyI,OAAO,EAAEd,SAAU;YACnB0B,QAAQ,EAAE,CAAC3K,WAAY;YAAAsJ,QAAA,gBAEvBxL,OAAA,CAACzB,SAAS;cAAC4J,IAAI,EAAE;YAAG;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,SAEzB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAChB,eAEDhM,OAAA,CAACwD,aAAa;YACZyI,OAAO,EAAEzB,QAAS;YAClBqC,QAAQ,EAAE9G,gBAAgB,KAAKoB,KAAK,CAACiB,MAAM,GAAG,CAAE;YAAAoD,QAAA,GACjD,MAEC,eAAAxL,OAAA,CAAC1B,UAAU;cAAC6J,IAAI,EAAE;YAAG;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEnBhM,OAAA,CAAC4D,aAAa;QAAA4H,QAAA,gBACZxL,OAAA,CAAC8D,WAAW;UAAA0H,QAAA,gBACVxL,OAAA,CAACvB,MAAM;YAAC0J,IAAI,EAAE;UAAG;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAEdhM,OAAA,CAACgE,eAAe;UAAAwH,QAAA,EACb5E,WAAW,gBACV5G,OAAA,CAACkE,WAAW;YAAAsH,QAAA,gBACVxL,OAAA,CAACb,aAAa;cAAA0M,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjBhM,OAAA;cAAKyL,KAAK,EAAE;gBAAEqB,QAAQ,EAAE,QAAQ;gBAAEjL,UAAU,EAAE,KAAK;gBAAEkL,YAAY,EAAE;cAAM,CAAE;cAAAvB,QAAA,EAAC;YAE5E;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhM,OAAA;cAAKyL,KAAK,EAAE;gBAAEqB,QAAQ,EAAE,QAAQ;gBAAEC,YAAY,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAM,CAAE;cAAAxB,QAAA,EAAC;YAE7E;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhM,OAAA;cAAKyL,KAAK,EAAE;gBAAEqB,QAAQ,EAAE,SAAS;gBAAEnB,SAAS,EAAE,MAAM;gBAAEqB,UAAU,EAAE;cAAM,CAAE;cAAAxB,QAAA,GAAC,6CAC9B,eAAAxL,OAAA;gBAAA6L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,yCACb,eAAAhM,OAAA;gBAAA6L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,uBAE1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhM,OAAA;cACEiM,OAAO,EAAExD,WAAY;cACrBgD,KAAK,EAAE;gBACLwB,SAAS,EAAE,MAAM;gBACjBrB,OAAO,EAAE,UAAU;gBACnBsB,UAAU,EAAE,SAAS;gBACrBxB,KAAK,EAAE,OAAO;gBACdyB,MAAM,EAAE,MAAM;gBACdC,YAAY,EAAE,KAAK;gBACnBC,MAAM,EAAE,SAAS;gBACjBP,QAAQ,EAAE,QAAQ;gBAClBjL,UAAU,EAAE;cACd,CAAE;cAAA2J,QAAA,EACH;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,gBAEdhM,OAAA,CAAAE,SAAA;YAAAsL,QAAA,gBACExL,OAAA,CAAC5B,MAAM;cAELkP,GAAG,EAAExH,SAAU;cACfyH,KAAK,EAAE,KAAM;cACb3E,KAAK,EAAC,MAAM;cACZG,MAAM,EAAC,MAAM;cACbyE,gBAAgB,EAAC,YAAY;cAC7BC,gBAAgB,EAAE/E,mBAAmB,CAAC,CAAE;cACxCgF,gBAAgB,EAAErF,iBAAkB;cACpCsF,WAAW,EAAEnF,gBAAiB;cAC9BoF,yBAAyB,EAAE;YAAK,GAT3B,UAAU5G,iBAAiB,EAAE;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUnC,CAAC,EAEDlF,aAAa,iBACZ9G,OAAA;cAAKyL,KAAK,EAAE;gBACVoC,QAAQ,EAAE,UAAU;gBACpBC,GAAG,EAAE,KAAK;gBACVC,IAAI,EAAE,KAAK;gBACXC,SAAS,EAAE,uBAAuB;gBAClCd,UAAU,EAAE,iBAAiB;gBAC7BxB,KAAK,EAAE,OAAO;gBACdE,OAAO,EAAE,WAAW;gBACpBwB,YAAY,EAAE,KAAK;gBACnBN,QAAQ,EAAE,QAAQ;gBAClBmB,MAAM,EAAE;cACV,CAAE;cAAAzC,QAAA,EAAC;YAEH;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN,EAEAzE,eAAe,iBACdvH,OAAA,CAACoE,aAAa;cAAAoH,QAAA,EACXjE;YAAe;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAChB;UAAA,eACD;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC,eAElBhM,OAAA,CAACsE,iBAAiB;UAAAkH,QAAA,gBAChBxL,OAAA,CAACwE,cAAc;YAAAgH,QAAA,EACZnE,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEuD,IAAI,GAAG,aAAavD,UAAU,CAACuD,IAAI,EAAE,GAAG;UAA8B;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,EAChB,CAAA3E,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkC,UAAU,kBACrBvJ,OAAA,CAAC0E,cAAc;YAAA8G,QAAA,GAAC,cACF,EAACc,IAAI,CAACC,KAAK,CAAClF,UAAU,CAACkC,UAAU,GAAG,GAAG,CAAC,EAAC,GACvD;UAAA;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAgB,CACjB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACgB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAEbxF,cAAc,iBACbxG,OAAA,CAAC4E,eAAe;MAAA4G,QAAA,eACdxL,OAAA,CAAC8E,YAAY;QAAA0G,QAAA,gBACXxL,OAAA,CAAClB,MAAM;UAACqJ,IAAI,EAAE,EAAG;UAACsD,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEqB,YAAY,EAAE;UAAO;QAAE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvEhM,OAAA,CAACgF,UAAU;UAAAwG,QAAA,EAAC;QAAkB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC3ChM,OAAA,CAACmF,SAAS;UAAAqG,QAAA,GAAC,uDAC4C,EAAC/F,KAAK,EAAC,IAAE,EAACyB,SAAS,CAACyD,IAAI,EAAC,wBAC1D,EAACxD,KAAK,CAACiB,MAAM,EAAC,uBACpC;QAAA;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACZhM,OAAA,CAACsF,YAAY;UAAAkG,QAAA,gBACXxL,OAAA,CAACwD,aAAa;YAACyI,OAAO,EAAEX,mBAAoB;YAAAE,QAAA,gBAC1CxL,OAAA,CAACxB,IAAI;cAAC2J,IAAI,EAAE;YAAG;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kBAEpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,EACfvG,KAAK,GAAG,CAAC,iBACRzF,OAAA,CAACwD,aAAa;YAACE,OAAO,EAAC,SAAS;YAACuI,OAAO,EAAEV,eAAgB;YAAAC,QAAA,gBACxDxL,OAAA,CAACjB,MAAM;cAACoJ,IAAI,EAAE;YAAG;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAEtB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAChB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAClB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACc,CAAC;AAEtB,CAAC;AAACnG,EAAA,CA9dIL,iBAAiB;EAAA,QAmCjBnG,gBAAgB;AAAA;AAAA6O,IAAA,GAnChB1I,iBAAiB;AAgevB,eAAeA,iBAAiB;AAAC,IAAA7E,EAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAG,GAAA,EAAAK,GAAA,EAAAM,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAA2I,IAAA;AAAAC,YAAA,CAAAxN,EAAA;AAAAwN,YAAA,CAAAnN,GAAA;AAAAmN,YAAA,CAAAjN,GAAA;AAAAiN,YAAA,CAAA5M,GAAA;AAAA4M,YAAA,CAAAzM,GAAA;AAAAyM,YAAA,CAAApM,GAAA;AAAAoM,YAAA,CAAA9L,GAAA;AAAA8L,YAAA,CAAA3L,GAAA;AAAA2L,YAAA,CAAAzL,GAAA;AAAAyL,YAAA,CAAAvL,GAAA;AAAAuL,YAAA,CAAApL,GAAA;AAAAoL,YAAA,CAAAlL,IAAA;AAAAkL,YAAA,CAAAhL,IAAA;AAAAgL,YAAA,CAAA9K,IAAA;AAAA8K,YAAA,CAAA5K,IAAA;AAAA4K,YAAA,CAAAxK,IAAA;AAAAwK,YAAA,CAAAtK,IAAA;AAAAsK,YAAA,CAAApK,IAAA;AAAAoK,YAAA,CAAAlK,IAAA;AAAAkK,YAAA,CAAAhK,IAAA;AAAAgK,YAAA,CAAA9J,IAAA;AAAA8J,YAAA,CAAA5J,IAAA;AAAA4J,YAAA,CAAA1J,IAAA;AAAA0J,YAAA,CAAAxJ,IAAA;AAAAwJ,YAAA,CAAAtJ,IAAA;AAAAsJ,YAAA,CAAApJ,IAAA;AAAAoJ,YAAA,CAAAjJ,IAAA;AAAAiJ,YAAA,CAAA9I,IAAA;AAAA8I,YAAA,CAAA5I,IAAA;AAAA4I,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}